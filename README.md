# PXBLangs

A comprehensive multi-tenant FastAPI application with RBAC (Role-Based Access Control) system, event-driven architecture, and async task processing.

## Features

- **Multi-tenant Architecture**: Complete isolation between tenants with shared infrastructure
- **Three User Roles**: Admin (system-wide), Tenant (tenant-specific), User (end-user)
- **JWT Authentication**: Secure token-based authentication with refresh tokens
- **RBAC Permission System**: Fine-grained role-based access control
- **Event-Driven Architecture**: Publish-subscribe pattern for loose coupling
- **Configurable Logging**: Structured logging with multiple output formats
- **Async Task Processing**: Celery-based background task system
- **RESTful API**: Comprehensive API with automatic OpenAPI documentation
- **Database Migrations**: Alembic-based schema versioning
- **Health Monitoring**: Built-in health checks and metrics

## Tech Stack

- **Backend**: FastAPI, Python 3.11+
- **Database**: MySQL 5.7 with SQLAlchemy 2.0 ORM
- **Cache & Message Broker**: Redis
- **Task Queue**: Celery with Redis backend
- **Authentication**: JWT with bcrypt password hashing
- **Migration**: Alembic
- **Package Management**: Poetry
- **Time Handling**: Arrow library
- **HTTP Client**: Requests
- **Excel Processing**: OpenPyXL
- **ASGI Server**: Uvicorn

## Quick Start

### Prerequisites

- Python 3.11+
- MySQL 5.7+
- Redis
- Poetry (for dependency management)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pxblangs
   ```

2. **Run development setup**
   ```bash
   python scripts/dev.py
   ```
   This script will:
   - Create `.env` file from template
   - Install dependencies with Poetry
   - Check MySQL and Redis services
   - Create database and run migrations
   - Set up default admin user

3. **Start the application**
   ```bash
   python scripts/start.py
   ```
   Or for development with auto-reload:
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

### Manual Setup

1. **Install dependencies**
   ```bash
   poetry install
   ```

2. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Setup database**
   ```bash
   # Create MySQL database
   mysql -u root -p -e "CREATE DATABASE pxblangs CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

   # Run migrations
   alembic upgrade head
   ```

4. **Start services**
   ```bash
   # Start Redis (if not running)
   redis-server

   # Start Celery worker (in separate terminal)
   celery -A app.core.celery_app worker --loglevel=info

   # Start Celery beat scheduler (in separate terminal)
   celery -A app.core.celery_app beat --loglevel=info

   # Start FastAPI application
   uvicorn app.main:app --reload
   ```

## API Documentation

Once the application is running, visit:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **Health Check**: http://localhost:8000/health
- **Detailed Health**: http://localhost:8000/health/detailed
- **Metrics**: http://localhost:8000/metrics

## Default Credentials

After setup, you can login with:
- **Username**: admin
- **Password**: admin123
- **Email**: <EMAIL>

⚠️ **Important**: Change these credentials in production!

## Architecture

### Multi-Tenant Design

The application implements a multi-tenant architecture where:
- **Admins** have system-wide access and can manage all tenants
- **Tenants** have isolated access to their own data and users
- **Users** belong to specific tenants and have limited access

### Event-Driven System

The application uses an event-driven architecture with:
- **Event Bus**: Central event publishing and subscription system
- **Event Handlers**: Automatic event processing for business logic
- **Event Decorators**: Easy event publishing and handling registration
- **Event Logging**: Complete audit trail of all system events

### Task Processing

Background tasks are handled by Celery with:
- **User Tasks**: Welcome emails, login tracking, cleanup
- **Email Tasks**: Notification emails, bulk emails, system alerts
- **Cleanup Tasks**: Token cleanup, log rotation, database maintenance
- **Report Tasks**: Daily reports, analytics, data exports

## Development

### Code Quality

```bash
# Format code
black .
isort .

# Lint code
flake8 .
mypy .

# Run tests
pytest
```

### Database Migrations

```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

### Celery Tasks

```bash
# Start worker
celery -A app.core.celery_app worker --loglevel=info

# Start beat scheduler
celery -A app.core.celery_app beat --loglevel=info

# Monitor tasks
celery -A app.core.celery_app flower
```

## Project Structure

```
pxblangs/
├── alembic/              # Database migrations
├── app/                  # Application code
│   ├── api/             # API routes and endpoints
│   │   ├── auth.py      # Authentication endpoints
│   │   ├── users.py     # User management endpoints
│   │   ├── tenants.py   # Tenant management endpoints
│   │   └── admins.py    # Admin management endpoints
│   ├── core/            # Core functionality
│   │   ├── auth.py      # Authentication logic
│   │   ├── config.py    # Configuration settings
│   │   ├── security.py  # Security utilities
│   │   ├── permissions.py # RBAC system
│   │   ├── logging.py   # Logging configuration
│   │   └── celery_app.py # Celery configuration
│   ├── dao/             # Data Access Objects
│   ├── db/              # Database configuration
│   ├── events/          # Event system
│   ├── handlers/        # Event handlers
│   ├── models/          # SQLAlchemy models
│   ├── schemas/         # Pydantic schemas
│   ├── services/        # Business logic
│   ├── tasks/           # Celery tasks
│   ├── utils/           # Utility functions
│   └── main.py          # FastAPI application entry point
├── docs/                # Documentation
├── logs/                # Log files
├── scripts/             # Utility scripts
├── statics/             # Static files
├── tests/               # Test files
├── .env.example         # Environment template
├── .gitignore           # Git ignore rules
├── pyproject.toml       # Poetry configuration
└── README.md            # This file
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
