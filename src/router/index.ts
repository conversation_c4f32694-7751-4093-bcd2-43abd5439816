import { createRouter, createWebHistory, type RouteRecordRaw } from "vue-router";
import { useAuthStore } from "@/stores/auth";
import { message } from "ant-design-vue";

// 路由元信息接口
declare module "vue-router" {
  interface RouteMeta {
    title?: string;
    permissions?: string[];
    requiresAuth?: boolean;
    hideInMenu?: boolean;
    icon?: string;
  }
}

// 基础路由（不需要认证）
const publicRoutes: RouteRecordRaw[] = [
  {
    path: "/login",
    name: "Login",
    component: () => import("@/pages/Login.vue"),
    meta: {
      title: "登录",
      requiresAuth: false,
      hideInMenu: true,
    },
  },
  {
    path: "/403",
    name: "Forbidden",
    component: () => import("@/pages/error/403.vue"),
    meta: {
      title: "无权限",
      requiresAuth: false,
      hideInMenu: true,
    },
  },
  {
    path: "/404",
    name: "NotFound",
    component: () => import("@/pages/error/404.vue"),
    meta: {
      title: "页面不存在",
      requiresAuth: false,
      hideInMenu: true,
    },
  },
];

// 主要路由（需要认证）
const privateRoutes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "Layout",
    component: () => import("@/layouts/MainLayout.vue"),
    redirect: "/dashboard",
    meta: {
      requiresAuth: true,
    },
    children: [
      {
        path: "/dashboard",
        name: "Dashboard",
        component: () => import("@/pages/Dashboard.vue"),
        meta: {
          title: "仪表板",
          icon: "DashboardOutlined",
          requiresAuth: true,
        },
      },
      {
        path: "/admins",
        name: "AdminManagement",
        component: () => import("@/pages/admin/AdminList.vue"),
        meta: {
          title: "管理员管理",
          icon: "UserOutlined",
          permissions: ["admin:read"],
          requiresAuth: true,
        },
      },
      {
        path: "/tenants",
        name: "TenantManagement",
        component: () => import("@/pages/tenant/TenantList.vue"),
        meta: {
          title: "租户管理",
          icon: "TeamOutlined",
          permissions: ["tenant:read"],
          requiresAuth: true,
        },
      },
      {
        path: "/users",
        name: "UserManagement",
        component: () => import("@/pages/user/UserList.vue"),
        meta: {
          title: "用户管理",
          icon: "UsergroupAddOutlined",
          permissions: ["user:read"],
          requiresAuth: true,
        },
      },
      {
        path: "/permissions",
        name: "PermissionManagement",
        component: () => import("@/pages/permission/PermissionList.vue"),
        meta: {
          title: "权限管理",
          icon: "SafetyOutlined",
          permissions: ["permission:read"],
          requiresAuth: true,
        },
      },
      {
        path: "/profile",
        name: "Profile",
        component: () => import("@/pages/Profile.vue"),
        meta: {
          title: "个人设置",
          icon: "SettingOutlined",
          requiresAuth: true,
          hideInMenu: true,
        },
      },
    ],
  },
];

// 合并所有路由
const routes: RouteRecordRaw[] = [
  ...publicRoutes,
  ...privateRoutes,
  {
    path: "/:pathMatch(.*)*",
    redirect: "/404",
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore();

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - 多租户管理后台`;
  }

  // 如果路由不需要认证，直接通过
  if (!to.meta.requiresAuth) {
    next();
    return;
  }

  // 检查是否已登录
  if (!authStore.isAuthenticated) {
    message.warning("请先登录");
    next("/login");
    return;
  }

  // 检查权限
  if (to.meta.permissions && to.meta.permissions.length > 0) {
    const hasPermission = authStore.hasAnyPermission(to.meta.permissions);
    if (!hasPermission) {
      message.error("没有权限访问该页面");
      next("/403");
      return;
    }
  }

  next();
});

export default router;
