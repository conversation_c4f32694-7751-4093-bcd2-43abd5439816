import type { Directive, DirectiveBinding } from 'vue'
import { useAuthStore } from '@/stores/auth'

/**
 * 权限指令
 * 用法：
 * v-permission="'user:create'" - 单个权限
 * v-permission="['user:create', 'user:update']" - 多个权限（任意一个）
 * v-permission.all="['user:create', 'user:update']" - 多个权限（全部）
 */
export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  },
  
  updated(el: HTMLElement, binding: DirectiveBinding) {
    checkPermission(el, binding)
  }
}

function checkPermission(el: HTMLElement, binding: DirectiveBinding) {
  const { value, modifiers } = binding
  const authStore = useAuthStore()
  
  if (!value) {
    console.warn('v-permission 指令需要权限值')
    return
  }
  
  let hasPermission = false
  
  if (typeof value === 'string') {
    // 单个权限
    hasPermission = authStore.hasPermission(value)
  } else if (Array.isArray(value)) {
    // 多个权限
    if (modifiers.all) {
      // 需要所有权限
      hasPermission = authStore.hasAllPermissions(value)
    } else {
      // 需要任意一个权限
      hasPermission = authStore.hasAnyPermission(value)
    }
  }
  
  if (!hasPermission) {
    // 没有权限，隐藏元素
    el.style.display = 'none'
    // 或者移除元素
    // el.parentNode?.removeChild(el)
  } else {
    // 有权限，显示元素
    el.style.display = ''
  }
}
