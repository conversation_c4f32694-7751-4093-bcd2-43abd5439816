import { http } from '@/utils/request'
import type { 
  ApiResponse, 
  TokenResponse,
  CurrentUser
} from '@/types'
import type {
  AdminLoginRequest,
  TenantLoginRequest,
  UserLoginRequest,
  ChangePasswordRequest
} from '@/types/api'

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 管理员登录
   */
  adminLogin: (data: AdminLoginRequest): Promise<ApiResponse<TokenResponse>> => {
    return http.post('/auth/admin/login', data)
  },

  /**
   * 租户登录
   */
  tenantLogin: (data: TenantLoginRequest): Promise<ApiResponse<TokenResponse>> => {
    return http.post('/auth/tenant/login', data)
  },

  /**
   * 用户登录
   */
  userLogin: (data: UserLoginRequest): Promise<ApiResponse<TokenResponse>> => {
    return http.post('/auth/user/login', data)
  },

  /**
   * 刷新token
   */
  refreshToken: (refreshToken: string): Promise<ApiResponse<TokenResponse>> => {
    return http.post('/auth/refresh', { refresh_token: refreshToken })
  },

  /**
   * 登出
   */
  logout: (): Promise<ApiResponse<null>> => {
    return http.post('/auth/logout')
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser: (): Promise<ApiResponse<CurrentUser>> => {
    return http.get('/auth/me')
  },

  /**
   * 修改密码
   */
  changePassword: (data: ChangePasswordRequest): Promise<ApiResponse<null>> => {
    return http.post('/auth/change-password', data)
  }
}
