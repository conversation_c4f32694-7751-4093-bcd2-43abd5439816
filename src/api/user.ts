import { http } from '@/utils/request'
import type { 
  ApiResponse, 
  User,
  PaginatedResponse
} from '@/types'
import type {
  CreateUserRequest,
  UpdateUserRequest,
  UserQueryParams
} from '@/types/api'

/**
 * 用户相关API
 */
export const userApi = {
  /**
   * 获取用户列表
   */
  getUsers: (params?: UserQueryParams): Promise<ApiResponse<PaginatedResponse<User>>> => {
    return http.get('/users', { params })
  },

  /**
   * 根据ID获取用户
   */
  getUserById: (id: number): Promise<ApiResponse<User>> => {
    return http.get(`/users/${id}`)
  },

  /**
   * 根据租户ID获取用户列表
   */
  getUsersByTenant: (tenantId: number, params?: UserQueryParams): Promise<ApiResponse<PaginatedResponse<User>>> => {
    return http.get(`/users/tenant/${tenantId}`, { params })
  },

  /**
   * 创建用户
   */
  createUser: (data: CreateUserRequest): Promise<ApiResponse<User>> => {
    return http.post('/users', data)
  },

  /**
   * 更新用户
   */
  updateUser: (id: number, data: UpdateUserRequest): Promise<ApiResponse<User>> => {
    return http.put(`/users/${id}`, data)
  },

  /**
   * 删除用户
   */
  deleteUser: (id: number): Promise<ApiResponse<null>> => {
    return http.delete(`/users/${id}`)
  },

  /**
   * 切换用户状态
   */
  toggleUserStatus: (id: number): Promise<ApiResponse<User>> => {
    return http.patch(`/users/${id}/toggle-status`)
  }
}
