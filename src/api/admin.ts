import { http } from '@/utils/request'
import type { 
  ApiResponse, 
  Admin,
  PaginatedResponse
} from '@/types'
import type {
  CreateAdminRequest,
  UpdateAdminRequest,
  AdminQueryParams
} from '@/types/api'

/**
 * 管理员相关API
 */
export const adminApi = {
  /**
   * 获取管理员列表
   */
  getAdmins: (params?: AdminQueryParams): Promise<ApiResponse<PaginatedResponse<Admin>>> => {
    return http.get('/admins', { params })
  },

  /**
   * 根据ID获取管理员
   */
  getAdminById: (id: number): Promise<ApiResponse<Admin>> => {
    return http.get(`/admins/${id}`)
  },

  /**
   * 创建管理员
   */
  createAdmin: (data: CreateAdminRequest): Promise<ApiResponse<Admin>> => {
    return http.post('/admins', data)
  },

  /**
   * 更新管理员
   */
  updateAdmin: (id: number, data: UpdateAdminRequest): Promise<ApiResponse<Admin>> => {
    return http.put(`/admins/${id}`, data)
  },

  /**
   * 删除管理员
   */
  deleteAdmin: (id: number): Promise<ApiResponse<null>> => {
    return http.delete(`/admins/${id}`)
  },

  /**
   * 切换管理员状态
   */
  toggleAdminStatus: (id: number): Promise<ApiResponse<Admin>> => {
    return http.patch(`/admins/${id}/toggle-status`)
  },

  /**
   * 切换超级管理员权限
   */
  toggleSuperuser: (id: number): Promise<ApiResponse<Admin>> => {
    return http.patch(`/admins/${id}/toggle-superuser`)
  }
}
