import { http } from '@/utils/request'
import type { 
  ApiResponse, 
  Tenant,
  PaginatedResponse
} from '@/types'
import type {
  CreateTenantRequest,
  UpdateTenantRequest,
  TenantQueryParams
} from '@/types/api'

/**
 * 租户相关API
 */
export const tenantApi = {
  /**
   * 获取租户列表
   */
  getTenants: (params?: TenantQueryParams): Promise<ApiResponse<PaginatedResponse<Tenant>>> => {
    return http.get('/tenants', { params })
  },

  /**
   * 根据ID获取租户
   */
  getTenantById: (id: number): Promise<ApiResponse<Tenant>> => {
    return http.get(`/tenants/${id}`)
  },

  /**
   * 创建租户
   */
  createTenant: (data: CreateTenantRequest): Promise<ApiResponse<Tenant>> => {
    return http.post('/tenants', data)
  },

  /**
   * 更新租户
   */
  updateTenant: (id: number, data: UpdateTenantRequest): Promise<ApiResponse<Tenant>> => {
    return http.put(`/tenants/${id}`, data)
  },

  /**
   * 删除租户
   */
  deleteTenant: (id: number): Promise<ApiResponse<null>> => {
    return http.delete(`/tenants/${id}`)
  },

  /**
   * 切换租户状态
   */
  toggleTenantStatus: (id: number): Promise<ApiResponse<Tenant>> => {
    return http.patch(`/tenants/${id}/toggle-status`)
  }
}
