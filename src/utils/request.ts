import axios, { type AxiosInstance, type AxiosRequestConfig, type AxiosResponse } from 'axios'
import { message } from 'ant-design-vue'
import type { ApiResponse } from '@/types'

// 创建axios实例
const request: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳，防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { data } = response
    
    // 如果响应成功
    if (data.success) {
      return response
    }
    
    // 如果响应失败，显示错误信息
    const errorMessage = data.message || data.error || '请求失败'
    message.error(errorMessage)
    
    return Promise.reject(new Error(errorMessage))
  },
  (error) => {
    console.error('响应拦截器错误:', error)
    
    // 处理不同的HTTP状态码
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，清除token并跳转到登录页
          localStorage.removeItem('access_token')
          localStorage.removeItem('refresh_token')
          localStorage.removeItem('user_info')
          
          // 如果不是在登录页，则跳转到登录页
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          
          message.error('登录已过期，请重新登录')
          break
          
        case 403:
          message.error('没有权限访问该资源')
          break
          
        case 404:
          message.error('请求的资源不存在')
          break
          
        case 422:
          // 表单验证错误
          const errorMessage = data?.message || '表单验证失败'
          message.error(errorMessage)
          break
          
        case 500:
          message.error('服务器内部错误')
          break
          
        default:
          const defaultMessage = data?.message || data?.error || `请求失败 (${status})`
          message.error(defaultMessage)
      }
    } else if (error.request) {
      // 网络错误
      message.error('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      message.error(error.message || '请求失败')
    }
    
    return Promise.reject(error)
  }
)

// 封装常用的HTTP方法
export const http = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return request.get(url, config).then(res => res.data)
  },
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return request.post(url, data, config).then(res => res.data)
  },
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return request.put(url, data, config).then(res => res.data)
  },
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return request.patch(url, data, config).then(res => res.data)
  },
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> => {
    return request.delete(url, config).then(res => res.data)
  }
}

// 文件上传
export const uploadFile = (
  url: string, 
  file: File, 
  onProgress?: (progress: number) => void
): Promise<ApiResponse> => {
  const formData = new FormData()
  formData.append('file', file)
  
  return request.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  }).then(res => res.data)
}

// 下载文件
export const downloadFile = async (url: string, filename?: string): Promise<void> => {
  try {
    const response = await request.get(url, {
      responseType: 'blob'
    })
    
    const blob = new Blob([response.data])
    const downloadUrl = window.URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = downloadUrl
    link.download = filename || 'download'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    window.URL.revokeObjectURL(downloadUrl)
  } catch (error) {
    console.error('文件下载失败:', error)
    message.error('文件下载失败')
  }
}

// 刷新token
export const refreshToken = async (): Promise<boolean> => {
  try {
    const refreshTokenValue = localStorage.getItem('refresh_token')
    if (!refreshTokenValue) {
      return false
    }
    
    const response = await request.post('/auth/refresh', {
      refresh_token: refreshTokenValue
    })
    
    if (response.data.success) {
      const { access_token, refresh_token } = response.data.data
      localStorage.setItem('access_token', access_token)
      localStorage.setItem('refresh_token', refresh_token)
      return true
    }
    
    return false
  } catch (error) {
    console.error('刷新token失败:', error)
    return false
  }
}

export default request
