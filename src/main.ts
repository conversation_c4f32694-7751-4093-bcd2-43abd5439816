import "./assets/main.css";

import { createApp } from "vue";
import { createPinia } from "pinia";
import Antd from "ant-design-vue";
import "ant-design-vue/dist/reset.css";

import App from "./App.vue";
import router from "./router";
import { useAuthStore } from "@/stores/auth";
import { setupDirectives } from "@/directives";

const app = createApp(App);

// 配置Pinia
const pinia = createPinia();
app.use(pinia);

// 配置Ant Design Vue
app.use(Antd);

// 配置指令
setupDirectives(app);

// 配置路由
app.use(router);

// 恢复认证状态
const authStore = useAuthStore();
authStore.restoreAuth();

app.mount("#app");
