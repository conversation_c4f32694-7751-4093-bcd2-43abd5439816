<template>
  <a-layout class="main-layout">
    <!-- 侧边栏 -->
    <a-layout-sider
      v-model:collapsed="collapsed"
      :trigger="null"
      collapsible
      theme="dark"
      width="256"
    >
      <div class="logo">
        <h2 v-if="!collapsed">管理后台</h2>
        <h2 v-else>后台</h2>
      </div>
      
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        :items="menuItems"
        @click="handleMenuClick"
      />
    </a-layout-sider>
    
    <!-- 主内容区 -->
    <a-layout>
      <!-- 顶部导航栏 -->
      <a-layout-header class="header">
        <div class="header-left">
          <a-button
            type="text"
            :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
            @click="toggleCollapsed"
          />
        </div>
        
        <div class="header-right">
          <a-dropdown>
            <a-button type="text" class="user-info">
              <UserOutlined />
              <span class="username">{{ currentUser?.username || currentUser?.email }}</span>
              <DownOutlined />
            </a-button>
            
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile" @click="goToProfile">
                  <UserOutlined />
                  个人设置
                </a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout" @click="handleLogout">
                  <LogoutOutlined />
                  退出登录
                </a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </div>
      </a-layout-header>
      
      <!-- 面包屑导航 -->
      <div class="breadcrumb-container">
        <a-breadcrumb>
          <a-breadcrumb-item>
            <router-link to="/">
              <HomeOutlined />
              首页
            </router-link>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-for="item in breadcrumbItems" :key="item.path">
            <router-link v-if="item.path" :to="item.path">
              {{ item.title }}
            </router-link>
            <span v-else>{{ item.title }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      
      <!-- 内容区域 -->
      <a-layout-content class="content">
        <router-view />
      </a-layout-content>
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  DownOutlined,
  LogoutOutlined,
  HomeOutlined,
  DashboardOutlined,
  TeamOutlined,
  UsergroupAddOutlined,
  SafetyOutlined
} from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import type { MenuProps } from 'ant-design-vue'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const appStore = useAppStore()

// 响应式状态
const collapsed = computed({
  get: () => appStore.isCollapsed,
  set: (value) => appStore.setCollapsed(value)
})

const selectedKeys = computed({
  get: () => appStore.selectedKeys,
  set: (value) => appStore.setSelectedKeys(value)
})

const openKeys = computed({
  get: () => appStore.openKeys,
  set: (value) => appStore.setOpenKeys(value)
})

const currentUser = computed(() => authStore.currentUser)

// 菜单项配置
const menuItems = computed(() => {
  const permissions = authStore.permissions
  const items: MenuProps['items'] = [
    {
      key: '/dashboard',
      icon: h(DashboardOutlined),
      label: '仪表板'
    }
  ]
  
  // 根据权限添加菜单项
  if (permissions.includes('admin:read')) {
    items.push({
      key: '/admins',
      icon: h(UserOutlined),
      label: '管理员管理'
    })
  }
  
  if (permissions.includes('tenant:read')) {
    items.push({
      key: '/tenants',
      icon: h(TeamOutlined),
      label: '租户管理'
    })
  }
  
  if (permissions.includes('user:read')) {
    items.push({
      key: '/users',
      icon: h(UsergroupAddOutlined),
      label: '用户管理'
    })
  }
  
  if (permissions.includes('permission:read')) {
    items.push({
      key: '/permissions',
      icon: h(SafetyOutlined),
      label: '权限管理'
    })
  }
  
  return items
})

// 面包屑导航
const breadcrumbItems = computed(() => {
  const items = []
  const pathSegments = route.path.split('/').filter(Boolean)
  
  for (let i = 0; i < pathSegments.length; i++) {
    const path = '/' + pathSegments.slice(0, i + 1).join('/')
    const routeRecord = router.getRoutes().find(r => r.path === path)
    
    if (routeRecord?.meta?.title) {
      items.push({
        title: routeRecord.meta.title,
        path: i === pathSegments.length - 1 ? undefined : path
      })
    }
  }
  
  return items
})

// 方法
const toggleCollapsed = () => {
  appStore.toggleCollapsed()
}

const handleMenuClick = ({ key }: { key: string }) => {
  router.push(key)
}

const goToProfile = () => {
  router.push('/profile')
}

const handleLogout = async () => {
  await authStore.logout()
  router.push('/login')
}

// 监听路由变化，更新选中的菜单
watch(
  () => route.path,
  (newPath) => {
    appStore.setSelectedKeys([newPath])
    
    // 设置展开的菜单键
    const pathSegments = newPath.split('/').filter(Boolean)
    const openKeys = []
    for (let i = 0; i < pathSegments.length - 1; i++) {
      openKeys.push('/' + pathSegments.slice(0, i + 1).join('/'))
    }
    appStore.setOpenKeys(openKeys)
  },
  { immediate: true }
)
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #001529;
}

.logo h2 {
  color: white;
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 40px;
  padding: 0 12px;
}

.username {
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.breadcrumb-container {
  padding: 16px 24px;
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
}

.content {
  margin: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 6px;
  min-height: calc(100vh - 200px);
}
</style>
