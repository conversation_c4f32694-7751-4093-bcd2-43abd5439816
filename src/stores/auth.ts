import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { message } from 'ant-design-vue'
import { authApi } from '@/api'
import type { 
  CurrentUser, 
  TokenResponse, 
  UserType 
} from '@/types'
import type {
  AdminLoginRequest,
  TenantLoginRequest,
  UserLoginRequest
} from '@/types/api'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const currentUser = ref<CurrentUser | null>(null)
  const accessToken = ref<string>('')
  const refreshToken = ref<string>('')
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => {
    return !!accessToken.value && !!currentUser.value
  })

  const userType = computed(() => {
    return currentUser.value?.user_type
  })

  const isAdmin = computed(() => {
    return userType.value === 'admin'
  })

  const isTenant = computed(() => {
    return userType.value === 'tenant'
  })

  const isUser = computed(() => {
    return userType.value === 'user'
  })

  const permissions = computed(() => {
    return currentUser.value?.permissions || []
  })

  // 方法
  const setTokens = (tokens: TokenResponse) => {
    accessToken.value = tokens.access_token
    refreshToken.value = tokens.refresh_token
    
    // 保存到localStorage
    localStorage.setItem('access_token', tokens.access_token)
    localStorage.setItem('refresh_token', tokens.refresh_token)
  }

  const setCurrentUser = (user: CurrentUser) => {
    currentUser.value = user
    localStorage.setItem('user_info', JSON.stringify(user))
  }

  const clearAuth = () => {
    currentUser.value = null
    accessToken.value = ''
    refreshToken.value = ''
    
    // 清除localStorage
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_info')
  }

  // 从localStorage恢复状态
  const restoreAuth = () => {
    const token = localStorage.getItem('access_token')
    const refresh = localStorage.getItem('refresh_token')
    const userInfo = localStorage.getItem('user_info')
    
    if (token && refresh && userInfo) {
      accessToken.value = token
      refreshToken.value = refresh
      try {
        currentUser.value = JSON.parse(userInfo)
      } catch (error) {
        console.error('解析用户信息失败:', error)
        clearAuth()
      }
    }
  }

  // 管理员登录
  const adminLogin = async (loginData: AdminLoginRequest): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await authApi.adminLogin(loginData)
      
      if (response.success && response.data) {
        setTokens(response.data)
        
        // 获取用户信息
        await getCurrentUser()
        
        message.success('登录成功')
        return true
      }
      
      return false
    } catch (error: any) {
      console.error('管理员登录失败:', error)
      message.error(error.message || '登录失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 租户登录
  const tenantLogin = async (loginData: TenantLoginRequest): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await authApi.tenantLogin(loginData)
      
      if (response.success && response.data) {
        setTokens(response.data)
        
        // 获取用户信息
        await getCurrentUser()
        
        message.success('登录成功')
        return true
      }
      
      return false
    } catch (error: any) {
      console.error('租户登录失败:', error)
      message.error(error.message || '登录失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 用户登录
  const userLogin = async (loginData: UserLoginRequest): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await authApi.userLogin(loginData)
      
      if (response.success && response.data) {
        setTokens(response.data)
        
        // 获取用户信息
        await getCurrentUser()
        
        message.success('登录成功')
        return true
      }
      
      return false
    } catch (error: any) {
      console.error('用户登录失败:', error)
      message.error(error.message || '登录失败')
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 获取当前用户信息
  const getCurrentUser = async (): Promise<boolean> => {
    try {
      const response = await authApi.getCurrentUser()
      
      if (response.success && response.data) {
        setCurrentUser(response.data)
        return true
      }
      
      return false
    } catch (error: any) {
      console.error('获取用户信息失败:', error)
      return false
    }
  }

  // 登出
  const logout = async (): Promise<void> => {
    try {
      await authApi.logout()
    } catch (error) {
      console.error('登出请求失败:', error)
    } finally {
      clearAuth()
      message.success('已退出登录')
    }
  }

  // 刷新token
  const refreshAccessToken = async (): Promise<boolean> => {
    try {
      if (!refreshToken.value) {
        return false
      }
      
      const response = await authApi.refreshToken(refreshToken.value)
      
      if (response.success && response.data) {
        setTokens(response.data)
        return true
      }
      
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      clearAuth()
      return false
    }
  }

  // 检查是否有指定权限
  const hasPermission = (permission: string): boolean => {
    return permissions.value.includes(permission)
  }

  // 检查是否有任意一个权限
  const hasAnyPermission = (permissionList: string[]): boolean => {
    return permissionList.some(permission => hasPermission(permission))
  }

  // 检查是否有所有权限
  const hasAllPermissions = (permissionList: string[]): boolean => {
    return permissionList.every(permission => hasPermission(permission))
  }

  return {
    // 状态
    currentUser,
    accessToken,
    refreshToken,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    userType,
    isAdmin,
    isTenant,
    isUser,
    permissions,
    
    // 方法
    setTokens,
    setCurrentUser,
    clearAuth,
    restoreAuth,
    adminLogin,
    tenantLogin,
    userLogin,
    getCurrentUser,
    logout,
    refreshAccessToken,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions
  }
})
