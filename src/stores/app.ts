import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { MenuItem } from '@/types'

export const useAppStore = defineStore('app', () => {
  // 状态
  const collapsed = ref(false)
  const loading = ref(false)
  const theme = ref<'light' | 'dark'>('light')
  const selectedKeys = ref<string[]>([])
  const openKeys = ref<string[]>([])

  // 菜单配置
  const menuItems = ref<MenuItem[]>([
    {
      key: '/dashboard',
      label: '仪表板',
      icon: 'DashboardOutlined',
      path: '/dashboard'
    },
    {
      key: '/admins',
      label: '管理员管理',
      icon: 'UserOutlined',
      path: '/admins',
      permissions: ['admin:read']
    },
    {
      key: '/tenants',
      label: '租户管理',
      icon: 'TeamOutlined',
      path: '/tenants',
      permissions: ['tenant:read']
    },
    {
      key: '/users',
      label: '用户管理',
      icon: 'UsergroupAddOutlined',
      path: '/users',
      permissions: ['user:read']
    },
    {
      key: '/permissions',
      label: '权限管理',
      icon: 'SafetyOutlined',
      path: '/permissions',
      permissions: ['permission:read']
    }
  ])

  // 计算属性
  const isCollapsed = computed(() => collapsed.value)
  const isLoading = computed(() => loading.value)
  const currentTheme = computed(() => theme.value)

  // 方法
  const toggleCollapsed = () => {
    collapsed.value = !collapsed.value
  }

  const setCollapsed = (value: boolean) => {
    collapsed.value = value
  }

  const setLoading = (value: boolean) => {
    loading.value = value
  }

  const setTheme = (value: 'light' | 'dark') => {
    theme.value = value
    localStorage.setItem('theme', value)
  }

  const setSelectedKeys = (keys: string[]) => {
    selectedKeys.value = keys
  }

  const setOpenKeys = (keys: string[]) => {
    openKeys.value = keys
  }

  // 根据权限过滤菜单
  const getFilteredMenuItems = (userPermissions: string[]): MenuItem[] => {
    const filterMenu = (items: MenuItem[]): MenuItem[] => {
      return items.filter(item => {
        // 如果菜单项有权限要求，检查用户是否有权限
        if (item.permissions && item.permissions.length > 0) {
          const hasPermission = item.permissions.some(permission => 
            userPermissions.includes(permission)
          )
          if (!hasPermission) {
            return false
          }
        }

        // 如果有子菜单，递归过滤
        if (item.children && item.children.length > 0) {
          item.children = filterMenu(item.children)
          // 如果过滤后没有子菜单，则隐藏父菜单
          return item.children.length > 0
        }

        return !item.hidden
      })
    }

    return filterMenu(menuItems.value)
  }

  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark'
    if (savedTheme) {
      theme.value = savedTheme
    }
  }

  // 设置页面标题
  const setPageTitle = (title: string) => {
    document.title = `${title} - 多租户管理后台`
  }

  return {
    // 状态
    collapsed,
    loading,
    theme,
    selectedKeys,
    openKeys,
    menuItems,

    // 计算属性
    isCollapsed,
    isLoading,
    currentTheme,

    // 方法
    toggleCollapsed,
    setCollapsed,
    setLoading,
    setTheme,
    setSelectedKeys,
    setOpenKeys,
    getFilteredMenuItems,
    initTheme,
    setPageTitle
  }
})
