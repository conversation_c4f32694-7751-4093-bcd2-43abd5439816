<template>
  <div class="admin-list">
    <div class="page-header">
      <h1>管理员管理</h1>
      <a-button
        v-permission="'admin:create'"
        type="primary"
        @click="showCreateModal"
      >
        <PlusOutlined />
        添加管理员
      </a-button>
    </div>
    
    <!-- 搜索区域 -->
    <a-card class="search-card" :bordered="false">
      <a-form
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="用户名">
          <a-input
            v-model:value="searchForm.search"
            placeholder="请输入用户名或邮箱"
            allow-clear
          />
        </a-form-item>
        
        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="active">正常</a-select-option>
            <a-select-option value="inactive">未激活</a-select-option>
            <a-select-option value="suspended">已暂停</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="超级管理员">
          <a-select
            v-model:value="searchForm.is_superuser"
            placeholder="请选择"
            allow-clear
            style="width: 100px"
          >
            <a-select-option :value="true">是</a-select-option>
            <a-select-option :value="false">否</a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item>
          <a-button type="primary" html-type="submit">
            <SearchOutlined />
            搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>
    
    <!-- 表格区域 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getUserStatusColor(record.status)">
              {{ getUserStatusText(record.status) }}
            </a-tag>
          </template>
          
          <template v-if="column.key === 'is_superuser'">
            <a-tag v-if="record.is_superuser" color="red">
              超级管理员
            </a-tag>
            <a-tag v-else color="default">
              普通管理员
            </a-tag>
          </template>
          
          <template v-if="column.key === 'is_active'">
            <a-switch
              v-permission="'admin:update'"
              :checked="record.is_active"
              @change="(checked) => toggleActive(record, checked)"
            />
          </template>
          
          <template v-if="column.key === 'created_at'">
            {{ formatDateTime(record.created_at) }}
          </template>
          
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button
                v-permission="'admin:update'"
                type="link"
                size="small"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>
              
              <a-button
                v-permission="'admin:update'"
                type="link"
                size="small"
                @click="toggleSuperuser(record)"
              >
                {{ record.is_superuser ? '取消超管' : '设为超管' }}
              </a-button>
              
              <a-popconfirm
                v-permission="'admin:delete'"
                title="确定要删除这个管理员吗？"
                @confirm="deleteAdmin(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
    
    <!-- 创建/编辑模态框 -->
    <!-- <AdminModal
      v-model:visible="modalVisible"
      :admin="currentAdmin"
      :mode="modalMode"
      @success="handleModalSuccess"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { adminApi } from '@/api'
import { formatDateTime, getUserStatusText, getUserStatusColor } from '@/utils'
import type { Admin } from '@/types'
import type { AdminQueryParams } from '@/types/api'
// import AdminModal from './AdminModal.vue'

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const currentAdmin = ref<Admin | null>(null)
const dataSource = ref<Admin[]>([])

const searchForm = reactive<AdminQueryParams>({
  page: 1,
  page_size: 10,
  search: '',
  status: undefined,
  is_superuser: undefined
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email'
  },
  {
    title: '姓名',
    dataIndex: 'full_name',
    key: 'full_name'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '类型',
    dataIndex: 'is_superuser',
    key: 'is_superuser'
  },
  {
    title: '启用状态',
    dataIndex: 'is_active',
    key: 'is_active'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at'
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
]

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await adminApi.getAdmins({
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    })
    
    if (response.success && response.data) {
      dataSource.value = response.data.data
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('加载管理员列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    page: 1,
    page_size: 10,
    search: '',
    status: undefined,
    is_superuser: undefined
  })
  pagination.current = 1
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const showCreateModal = () => {
  currentAdmin.value = null
  modalMode.value = 'create'
  modalVisible.value = true
}

const showEditModal = (admin: Admin) => {
  currentAdmin.value = admin
  modalMode.value = 'edit'
  modalVisible.value = true
}

const handleModalSuccess = () => {
  modalVisible.value = false
  loadData()
}

const toggleActive = async (admin: Admin, checked: boolean) => {
  try {
    await adminApi.updateAdmin(admin.id, { is_active: checked })
    message.success('状态更新成功')
    loadData()
  } catch (error) {
    console.error('更新状态失败:', error)
  }
}

const toggleSuperuser = async (admin: Admin) => {
  try {
    await adminApi.toggleSuperuser(admin.id)
    message.success('权限更新成功')
    loadData()
  } catch (error) {
    console.error('更新权限失败:', error)
  }
}

const deleteAdmin = async (admin: Admin) => {
  try {
    await adminApi.deleteAdmin(admin.id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.admin-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.search-card {
  margin-bottom: 16px;
}
</style>
