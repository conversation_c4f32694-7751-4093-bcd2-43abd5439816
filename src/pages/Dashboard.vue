<template>
  <div class="dashboard">
    <h1>仪表板</h1>
    
    <!-- 统计卡片 -->
    <a-row :gutter="[16, 16]" class="stats-row">
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="管理员总数"
            :value="stats.adminCount"
            :prefix="h(UserOutlined)"
            value-style="color: #3f8600"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="租户总数"
            :value="stats.tenantCount"
            :prefix="h(TeamOutlined)"
            value-style="color: #cf1322"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="用户总数"
            :value="stats.userCount"
            :prefix="h(UsergroupAddOutlined)"
            value-style="color: #1890ff"
          />
        </a-card>
      </a-col>
      
      <a-col :xs="24" :sm="12" :md="6">
        <a-card>
          <a-statistic
            title="活跃用户"
            :value="stats.activeUserCount"
            :prefix="h(CheckCircleOutlined)"
            value-style="color: #722ed1"
          />
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 图表区域 -->
    <a-row :gutter="[16, 16]" class="charts-row">
      <a-col :xs="24" :lg="12">
        <a-card title="用户增长趋势" :bordered="false">
          <div class="chart-placeholder">
            <p>用户增长趋势图表</p>
            <p class="chart-note">（此处可集成 ECharts 或其他图表库）</p>
          </div>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :lg="12">
        <a-card title="租户分布" :bordered="false">
          <div class="chart-placeholder">
            <p>租户分布饼图</p>
            <p class="chart-note">（此处可集成 ECharts 或其他图表库）</p>
          </div>
        </a-card>
      </a-col>
    </a-row>
    
    <!-- 最近活动 -->
    <a-row :gutter="[16, 16]">
      <a-col :xs="24" :lg="16">
        <a-card title="最近活动" :bordered="false">
          <a-list
            :data-source="recentActivities"
            :loading="loading"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: item.color }">
                      {{ item.user.charAt(0).toUpperCase() }}
                    </a-avatar>
                  </template>
                  <template #title>
                    {{ item.action }}
                  </template>
                  <template #description>
                    {{ item.user }} · {{ formatDateTime(item.time) }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
      
      <a-col :xs="24" :lg="8">
        <a-card title="快速操作" :bordered="false">
          <div class="quick-actions">
            <a-button
              v-permission="'admin:create'"
              type="primary"
              block
              class="action-btn"
              @click="goToAdminManagement"
            >
              <UserOutlined />
              添加管理员
            </a-button>
            
            <a-button
              v-permission="'tenant:create'"
              block
              class="action-btn"
              @click="goToTenantManagement"
            >
              <TeamOutlined />
              添加租户
            </a-button>
            
            <a-button
              v-permission="'user:create'"
              block
              class="action-btn"
              @click="goToUserManagement"
            >
              <UsergroupAddOutlined />
              添加用户
            </a-button>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import {
  UserOutlined,
  TeamOutlined,
  UsergroupAddOutlined,
  CheckCircleOutlined
} from '@ant-design/icons-vue'
import { formatDateTime } from '@/utils'

const router = useRouter()

// 响应式数据
const loading = ref(false)

const stats = reactive({
  adminCount: 0,
  tenantCount: 0,
  userCount: 0,
  activeUserCount: 0
})

const recentActivities = ref([
  {
    id: 1,
    user: 'admin',
    action: '创建了新的租户',
    time: new Date().toISOString(),
    color: '#1890ff'
  },
  {
    id: 2,
    user: 'tenant1',
    action: '添加了新用户',
    time: new Date(Date.now() - 3600000).toISOString(),
    color: '#52c41a'
  },
  {
    id: 3,
    user: 'admin',
    action: '更新了系统配置',
    time: new Date(Date.now() - 7200000).toISOString(),
    color: '#722ed1'
  }
])

// 方法
const loadStats = async () => {
  loading.value = true
  try {
    // 这里应该调用实际的API获取统计数据
    // 模拟数据
    stats.adminCount = 5
    stats.tenantCount = 12
    stats.userCount = 156
    stats.activeUserCount = 89
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

const goToAdminManagement = () => {
  router.push('/admins')
}

const goToTenantManagement = () => {
  router.push('/tenants')
}

const goToUserManagement = () => {
  router.push('/users')
}

// 生命周期
onMounted(() => {
  loadStats()
})
</script>

<style scoped>
.dashboard {
  padding: 0;
}

.dashboard h1 {
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 600;
}

.stats-row {
  margin-bottom: 24px;
}

.charts-row {
  margin-bottom: 24px;
}

.chart-placeholder {
  height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 6px;
}

.chart-placeholder p {
  margin: 0;
  color: #666;
}

.chart-note {
  font-size: 12px;
  color: #999;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}
</style>
