<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h1>多租户管理后台</h1>
        <p>请选择登录方式</p>
      </div>
      
      <a-tabs v-model:activeKey="activeTab" centered>
        <a-tab-pane key="admin" tab="管理员登录">
          <a-form
            :model="adminForm"
            :rules="adminRules"
            @finish="handleAdminLogin"
            layout="vertical"
          >
            <a-form-item label="用户名" name="username">
              <a-input
                v-model:value="adminForm.username"
                placeholder="请输入用户名"
                size="large"
              >
                <template #prefix>
                  <UserOutlined />
                </template>
              </a-input>
            </a-form-item>
            
            <a-form-item label="密码" name="password">
              <a-input-password
                v-model:value="adminForm.password"
                placeholder="请输入密码"
                size="large"
              >
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input-password>
            </a-form-item>
            
            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                block
                :loading="loading"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        
        <a-tab-pane key="tenant" tab="租户登录">
          <a-form
            :model="tenantForm"
            :rules="tenantRules"
            @finish="handleTenantLogin"
            layout="vertical"
          >
            <a-form-item label="租户代码" name="code">
              <a-input
                v-model:value="tenantForm.code"
                placeholder="请输入租户代码"
                size="large"
              >
                <template #prefix>
                  <TeamOutlined />
                </template>
              </a-input>
            </a-form-item>
            
            <a-form-item label="密码" name="password">
              <a-input-password
                v-model:value="tenantForm.password"
                placeholder="请输入密码"
                size="large"
              >
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input-password>
            </a-form-item>
            
            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                block
                :loading="loading"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
        
        <a-tab-pane key="user" tab="用户登录">
          <a-form
            :model="userForm"
            :rules="userRules"
            @finish="handleUserLogin"
            layout="vertical"
          >
            <a-form-item label="用户名" name="username">
              <a-input
                v-model:value="userForm.username"
                placeholder="请输入用户名"
                size="large"
              >
                <template #prefix>
                  <UserOutlined />
                </template>
              </a-input>
            </a-form-item>
            
            <a-form-item label="租户代码" name="tenant_code">
              <a-input
                v-model:value="userForm.tenant_code"
                placeholder="请输入租户代码"
                size="large"
              >
                <template #prefix>
                  <TeamOutlined />
                </template>
              </a-input>
            </a-form-item>
            
            <a-form-item label="密码" name="password">
              <a-input-password
                v-model:value="userForm.password"
                placeholder="请输入密码"
                size="large"
              >
                <template #prefix>
                  <LockOutlined />
                </template>
              </a-input-password>
            </a-form-item>
            
            <a-form-item>
              <a-button
                type="primary"
                html-type="submit"
                size="large"
                block
                :loading="loading"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { UserOutlined, LockOutlined, TeamOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import type { 
  AdminLoginRequest, 
  TenantLoginRequest, 
  UserLoginRequest 
} from '@/types/api'

const router = useRouter()
const authStore = useAuthStore()

const activeTab = ref('admin')
const loading = ref(false)

// 管理员登录表单
const adminForm = reactive<AdminLoginRequest>({
  username: '',
  password: ''
})

const adminRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}

// 租户登录表单
const tenantForm = reactive<TenantLoginRequest>({
  code: '',
  password: ''
})

const tenantRules = {
  code: [
    { required: true, message: '请输入租户代码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}

// 用户登录表单
const userForm = reactive<UserLoginRequest>({
  username: '',
  tenant_code: '',
  password: ''
})

const userRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' }
  ],
  tenant_code: [
    { required: true, message: '请输入租户代码', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, message: '密码长度至少6位', trigger: 'blur' }
  ]
}

// 管理员登录
const handleAdminLogin = async () => {
  loading.value = true
  try {
    const success = await authStore.adminLogin(adminForm)
    if (success) {
      router.push('/')
    }
  } finally {
    loading.value = false
  }
}

// 租户登录
const handleTenantLogin = async () => {
  loading.value = true
  try {
    const success = await authStore.tenantLogin(tenantForm)
    if (success) {
      router.push('/')
    }
  } finally {
    loading.value = false
  }
}

// 用户登录
const handleUserLogin = async () => {
  loading.value = true
  try {
    const success = await authStore.userLogin(userForm)
    if (success) {
      router.push('/')
    }
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.login-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}
</style>
