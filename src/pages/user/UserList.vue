<template>
  <div class="user-list">
    <div class="page-header">
      <h1>用户管理</h1>
      <a-button
        v-permission="'user:create'"
        type="primary"
        @click="showCreateModal"
      >
        <PlusOutlined />
        添加用户
      </a-button>
    </div>

    <!-- 搜索区域 -->
    <a-card class="search-card" :bordered="false">
      <a-form
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="用户名">
          <a-input
            v-model:value="searchForm.search"
            placeholder="请输入用户名或邮箱"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="租户">
          <a-select
            v-model:value="searchForm.tenant_id"
            placeholder="请选择租户"
            allow-clear
            style="width: 150px"
          >
            <a-select-option
              v-for="tenant in tenantOptions"
              :key="tenant.id"
              :value="tenant.id"
            >
              {{ tenant.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="active">正常</a-select-option>
            <a-select-option value="inactive">未激活</a-select-option>
            <a-select-option value="suspended">已暂停</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit">
            <SearchOutlined />
            搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 表格区域 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'tenant'">
            <a-tag color="blue">
              {{ record.tenant?.name || '未知租户' }}
            </a-tag>
          </template>

          <template v-if="column.key === 'status'">
            <a-tag :color="getUserStatusColor(record.status)">
              {{ getUserStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'is_active'">
            <a-switch
              v-permission="'user:update'"
              :checked="record.is_active"
              @change="(checked) => toggleActive(record, checked)"
            />
          </template>

          <template v-if="column.key === 'created_at'">
            {{ formatDateTime(record.created_at) }}
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button
                v-permission="'user:update'"
                type="link"
                size="small"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>

              <a-popconfirm
                v-permission="'user:delete'"
                title="确定要删除这个用户吗？"
                @confirm="deleteUser(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { userApi, tenantApi } from '@/api'
import { formatDateTime, getUserStatusText, getUserStatusColor } from '@/utils'
import type { User, Tenant } from '@/types'
import type { UserQueryParams } from '@/types/api'

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const currentUser = ref<User | null>(null)
const dataSource = ref<User[]>([])
const tenantOptions = ref<Tenant[]>([])

const searchForm = reactive<UserQueryParams>({
  page: 1,
  page_size: 10,
  search: '',
  tenant_id: undefined,
  status: undefined
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username'
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email'
  },
  {
    title: '姓名',
    dataIndex: 'full_name',
    key: 'full_name'
  },
  {
    title: '所属租户',
    dataIndex: 'tenant',
    key: 'tenant'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '启用状态',
    dataIndex: 'is_active',
    key: 'is_active'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at'
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await userApi.getUsers({
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    })

    if (response.success && response.data) {
      dataSource.value = response.data.data
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('加载用户列表失败:', error)
  } finally {
    loading.value = false
  }
}

const loadTenants = async () => {
  try {
    const response = await tenantApi.getTenants({ page: 1, page_size: 100 })
    if (response.success && response.data) {
      tenantOptions.value = response.data.data
    }
  } catch (error) {
    console.error('加载租户列表失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    page: 1,
    page_size: 10,
    search: '',
    tenant_id: undefined,
    status: undefined
  })
  pagination.current = 1
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const showCreateModal = () => {
  currentUser.value = null
  modalMode.value = 'create'
  modalVisible.value = true
}

const showEditModal = (user: User) => {
  currentUser.value = user
  modalMode.value = 'edit'
  modalVisible.value = true
}

const toggleActive = async (user: User, checked: boolean) => {
  try {
    await userApi.updateUser(user.id, { is_active: checked })
    message.success('状态更新成功')
    loadData()
  } catch (error) {
    console.error('更新状态失败:', error)
  }
}

const deleteUser = async (user: User) => {
  try {
    await userApi.deleteUser(user.id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadData()
  loadTenants()
})
</script>

<style scoped>
.user-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.search-card {
  margin-bottom: 16px;
}
</style>
