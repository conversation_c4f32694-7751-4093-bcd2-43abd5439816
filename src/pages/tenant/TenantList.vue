<template>
  <div class="tenant-list">
    <div class="page-header">
      <h1>租户管理</h1>
      <a-button
        v-permission="'tenant:create'"
        type="primary"
        @click="showCreateModal"
      >
        <PlusOutlined />
        添加租户
      </a-button>
    </div>

    <!-- 搜索区域 -->
    <a-card class="search-card" :bordered="false">
      <a-form
        :model="searchForm"
        layout="inline"
        @finish="handleSearch"
      >
        <a-form-item label="租户名称">
          <a-input
            v-model:value="searchForm.search"
            placeholder="请输入租户名称或代码"
            allow-clear
          />
        </a-form-item>

        <a-form-item label="状态">
          <a-select
            v-model:value="searchForm.status"
            placeholder="请选择状态"
            allow-clear
            style="width: 120px"
          >
            <a-select-option value="active">正常</a-select-option>
            <a-select-option value="inactive">未激活</a-select-option>
            <a-select-option value="suspended">已暂停</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit">
            <SearchOutlined />
            搜索
          </a-button>
          <a-button @click="resetSearch" style="margin-left: 8px">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 表格区域 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data-source="dataSource"
        :loading="loading"
        :pagination="pagination"
        row-key="id"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getUserStatusColor(record.status)">
              {{ getUserStatusText(record.status) }}
            </a-tag>
          </template>

          <template v-if="column.key === 'is_active'">
            <a-switch
              v-permission="'tenant:update'"
              :checked="record.is_active"
              @change="(checked) => toggleActive(record, checked)"
            />
          </template>

          <template v-if="column.key === 'created_at'">
            {{ formatDateTime(record.created_at) }}
          </template>

          <template v-if="column.key === 'action'">
            <a-space>
              <a-button
                v-permission="'tenant:update'"
                type="link"
                size="small"
                @click="showEditModal(record)"
              >
                编辑
              </a-button>

              <a-popconfirm
                v-permission="'tenant:delete'"
                title="确定要删除这个租户吗？"
                @confirm="deleteTenant(record)"
              >
                <a-button type="link" size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { PlusOutlined, SearchOutlined } from '@ant-design/icons-vue'
import { tenantApi } from '@/api'
import { formatDateTime, getUserStatusText, getUserStatusColor } from '@/utils'
import type { Tenant } from '@/types'
import type { TenantQueryParams } from '@/types/api'

// 响应式数据
const loading = ref(false)
const modalVisible = ref(false)
const modalMode = ref<'create' | 'edit'>('create')
const currentTenant = ref<Tenant | null>(null)
const dataSource = ref<Tenant[]>([])

const searchForm = reactive<TenantQueryParams>({
  page: 1,
  page_size: 10,
  search: '',
  status: undefined
})

const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条记录`
})

// 表格列配置
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '租户名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '租户代码',
    dataIndex: 'code',
    key: 'code'
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email'
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description'
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status'
  },
  {
    title: '启用状态',
    dataIndex: 'is_active',
    key: 'is_active'
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    key: 'created_at'
  },
  {
    title: '操作',
    key: 'action',
    width: 150
  }
]

// 方法
const loadData = async () => {
  loading.value = true
  try {
    const response = await tenantApi.getTenants({
      ...searchForm,
      page: pagination.current,
      page_size: pagination.pageSize
    })

    if (response.success && response.data) {
      dataSource.value = response.data.data
      pagination.total = response.data.total
    }
  } catch (error) {
    console.error('加载租户列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadData()
}

const resetSearch = () => {
  Object.assign(searchForm, {
    page: 1,
    page_size: 10,
    search: '',
    status: undefined
  })
  pagination.current = 1
  loadData()
}

const handleTableChange = (pag: any) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadData()
}

const showCreateModal = () => {
  currentTenant.value = null
  modalMode.value = 'create'
  modalVisible.value = true
}

const showEditModal = (tenant: Tenant) => {
  currentTenant.value = tenant
  modalMode.value = 'edit'
  modalVisible.value = true
}

const toggleActive = async (tenant: Tenant, checked: boolean) => {
  try {
    await tenantApi.updateTenant(tenant.id, { is_active: checked })
    message.success('状态更新成功')
    loadData()
  } catch (error) {
    console.error('更新状态失败:', error)
  }
}

const deleteTenant = async (tenant: Tenant) => {
  try {
    await tenantApi.deleteTenant(tenant.id)
    message.success('删除成功')
    loadData()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.tenant-list {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.search-card {
  margin-bottom: 16px;
}
</style>
