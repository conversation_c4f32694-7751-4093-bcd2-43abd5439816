import type { 
  Admin, 
  Tenant, 
  User, 
  Permission, 
  Role, 
  UserStatus,
  PaginationParams 
} from './index'

// 创建管理员请求
export interface CreateAdminRequest {
  username: string
  email: string
  password: string
  full_name?: string
  is_superuser?: boolean
}

// 更新管理员请求
export interface UpdateAdminRequest {
  username?: string
  email?: string
  full_name?: string
  is_active?: boolean
  status?: UserStatus
}

// 创建租户请求
export interface CreateTenantRequest {
  name: string
  code: string
  email: string
  password: string
  description?: string
}

// 更新租户请求
export interface UpdateTenantRequest {
  name?: string
  email?: string
  description?: string
  is_active?: boolean
  status?: UserStatus
}

// 创建用户请求
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  full_name?: string
  tenant_id: number
}

// 更新用户请求
export interface UpdateUserRequest {
  username?: string
  email?: string
  full_name?: string
  is_active?: boolean
  status?: UserStatus
}

// 修改密码请求
export interface ChangePasswordRequest {
  old_password: string
  new_password: string
  confirm_password: string
}

// 管理员登录请求
export interface AdminLoginRequest {
  username: string
  password: string
}

// 租户登录请求
export interface TenantLoginRequest {
  code: string
  password: string
}

// 用户登录请求
export interface UserLoginRequest {
  username: string
  password: string
  tenant_code: string
}

// 查询参数基础接口
export interface BaseQueryParams extends PaginationParams {
  search?: string
  status?: UserStatus
  is_active?: boolean
}

// 管理员查询参数
export interface AdminQueryParams extends BaseQueryParams {
  is_superuser?: boolean
}

// 租户查询参数
export interface TenantQueryParams extends BaseQueryParams {
  // 可以添加租户特有的查询参数
}

// 用户查询参数
export interface UserQueryParams extends BaseQueryParams {
  tenant_id?: number
}

// 权限查询参数
export interface PermissionQueryParams extends BaseQueryParams {
  resource_type?: string
  permission_type?: string
}

// 角色查询参数
export interface RoleQueryParams extends BaseQueryParams {
  // 可以添加角色特有的查询参数
}

// API端点常量
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    ADMIN_LOGIN: '/auth/admin/login',
    TENANT_LOGIN: '/auth/tenant/login',
    USER_LOGIN: '/auth/user/login',
    REFRESH: '/auth/refresh',
    LOGOUT: '/auth/logout',
    CHANGE_PASSWORD: '/auth/change-password'
  },
  
  // 管理员相关
  ADMINS: {
    BASE: '/admins',
    BY_ID: (id: number) => `/admins/${id}`,
    TOGGLE_STATUS: (id: number) => `/admins/${id}/toggle-status`,
    TOGGLE_SUPERUSER: (id: number) => `/admins/${id}/toggle-superuser`
  },
  
  // 租户相关
  TENANTS: {
    BASE: '/tenants',
    BY_ID: (id: number) => `/tenants/${id}`,
    TOGGLE_STATUS: (id: number) => `/tenants/${id}/toggle-status`
  },
  
  // 用户相关
  USERS: {
    BASE: '/users',
    BY_ID: (id: number) => `/users/${id}`,
    BY_TENANT: (tenantId: number) => `/users/tenant/${tenantId}`,
    TOGGLE_STATUS: (id: number) => `/users/${id}/toggle-status`
  },
  
  // 权限相关
  PERMISSIONS: {
    BASE: '/permissions',
    BY_ID: (id: number) => `/permissions/${id}`,
    USER_PERMISSIONS: (userId: number) => `/permissions/user/${userId}`
  },
  
  // 角色相关
  ROLES: {
    BASE: '/roles',
    BY_ID: (id: number) => `/roles/${id}`,
    PERMISSIONS: (roleId: number) => `/roles/${roleId}/permissions`
  }
} as const
