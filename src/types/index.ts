// 基础类型定义

// 用户类型枚举
export enum UserType {
  ADMIN = 'admin',
  TENANT = 'tenant',
  USER = 'user'
}

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

// 权限类型枚举
export enum PermissionType {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  MANAGE = 'manage',
  EXECUTE = 'execute'
}

// 资源类型枚举
export enum ResourceType {
  USER = 'user',
  TENANT = 'tenant',
  ADMIN = 'admin',
  PERMISSION = 'permission',
  ROLE = 'role',
  SYSTEM = 'system'
}

// 基础实体接口
export interface BaseEntity {
  id: number
  created_at: string
  updated_at: string
  created_by?: number
  updated_by?: number
}

// 管理员接口
export interface Admin extends BaseEntity {
  username: string
  email: string
  full_name?: string
  is_active: boolean
  is_superuser: boolean
  status: UserStatus
}

// 租户接口
export interface Tenant extends BaseEntity {
  name: string
  code: string
  email: string
  description?: string
  is_active: boolean
  status: UserStatus
}

// 用户接口
export interface User extends BaseEntity {
  username: string
  email: string
  full_name?: string
  is_active: boolean
  status: UserStatus
  tenant_id: number
  tenant?: Tenant
}

// 权限接口
export interface Permission extends BaseEntity {
  name: string
  code: string
  description?: string
  resource_type: ResourceType
  permission_type: PermissionType
  is_active: boolean
}

// 角色接口
export interface Role extends BaseEntity {
  name: string
  code: string
  description?: string
  is_active: boolean
}

// 当前用户信息
export interface CurrentUser {
  id: number
  user_type: UserType
  tenant_id?: number
  username?: string
  email?: string
  is_active: boolean
  permissions: string[]
}

// API响应基础结构
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data?: T
  error?: string
}

// 分页参数
export interface PaginationParams {
  page: number
  page_size: number
  skip?: number
  limit?: number
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[]
  page: number
  page_size: number
  total: number
  total_pages: number
}

// Token响应
export interface TokenResponse {
  access_token: string
  refresh_token: string
  expires_in: number
}

// 登录请求
export interface LoginRequest {
  username?: string
  code?: string
  password: string
  tenant_code?: string
}

// 菜单项接口
export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  permissions?: string[]
  hidden?: boolean
}

// 表格列配置
export interface TableColumn {
  key: string
  title: string
  dataIndex: string
  width?: number
  align?: 'left' | 'center' | 'right'
  sorter?: boolean
  filters?: Array<{ text: string; value: any }>
  render?: (value: any, record: any, index: number) => any
}

// 表单字段配置
export interface FormField {
  name: string
  label: string
  type: 'input' | 'password' | 'email' | 'textarea' | 'select' | 'switch' | 'date'
  required?: boolean
  rules?: any[]
  options?: Array<{ label: string; value: any }>
  placeholder?: string
}
