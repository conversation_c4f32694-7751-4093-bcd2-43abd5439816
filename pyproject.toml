[tool.poetry]
name = "pxblangs"
version = "0.1.0"
description = "Multi-tenant FastAPI application with RBAC"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "app"}]


[tool.poetry.dependencies]
python = "^3.11.13"
fastapi = "^0.104.1"
uvicorn = {extras = ["standard"], version = "^0.24.0"}
sqlalchemy = "^2.0.23"
alembic = "^1.12.1"
pymysql = "^1.1.0"
aiomysql = "^0.2.0"
cryptography = "^41.0.7"
redis = "^5.0.1"
celery = "^5.3.4"
arrow = "^1.3.0"
requests = "^2.31.0"
openpyxl = "^3.1.2"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
python-multipart = "^0.0.6"
pydantic = {extras = ["email"], version = "^2.5.0"}
pydantic-settings = "^2.1.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
black = "^23.11.0"
isort = "^5.12.0"
flake8 = "^6.1.0"
mypy = "^1.7.1"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
