"""
User-related service classes.
"""
from typing import Dict, List, Optional

from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.core.security import get_password_hash, verify_password
from app.dao.user import Admin<PERSON><PERSON>, TenantDAO, UserDAO, admin_dao, tenant_dao, user_dao
from app.models.user import Admin, Tenant, User, UserStatus
from app.schemas.user import (
    AdminCreate,
    AdminUpdate,
    PasswordChange,
    ProfileUpdate,
    TenantCreate,
    TenantUpdate,
    UserCreate,
    UserUpdate,
)
from app.services.base import BaseService


class AdminService(BaseService[Admin, AdminDAO]):
    """Service for Admin operations."""
    
    def __init__(self):
        super().__init__(admin_dao)
    
    def create_admin(self, db: Session, *, admin_in: AdminCreate, created_by: Optional[int] = None) -> Admin:
        """
        Create a new admin.
        
        Args:
            db: Database session
            admin_in: Admin creation data
            created_by: User ID who created the admin
            
        Returns:
            Admin: Created admin
            
        Raises:
            HTTPException: If username or email already exists
        """
        # Check if username already exists
        if self.dao.get_by_username(db, username=admin_in.username):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在"
            )
        
        # Check if email already exists
        if self.dao.get_by_email(db, email=admin_in.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
        
        # Hash password
        hashed_password = get_password_hash(admin_in.password)
        
        # Create admin data
        admin_data = admin_in.dict(exclude={"password"})
        admin_data["hashed_password"] = hashed_password
        admin_data["status"] = UserStatus.ACTIVE
        
        return self.create(db, obj_in=admin_data, created_by=created_by)
    
    def update_admin(
        self, db: Session, *, admin_id: int, admin_in: AdminUpdate, updated_by: Optional[int] = None
    ) -> Admin:
        """
        Update an admin.
        
        Args:
            db: Database session
            admin_id: Admin ID
            admin_in: Admin update data
            updated_by: User ID who updated the admin
            
        Returns:
            Admin: Updated admin
            
        Raises:
            HTTPException: If username or email already exists
        """
        # Check if username already exists (excluding current admin)
        if admin_in.username:
            existing = self.dao.get_by_username(db, username=admin_in.username)
            if existing and existing.id != admin_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="用户名已存在"
                )
        
        # Check if email already exists (excluding current admin)
        if admin_in.email:
            existing = self.dao.get_by_email(db, email=admin_in.email)
            if existing and existing.id != admin_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已存在"
                )
        
        update_data = admin_in.dict(exclude_unset=True)
        return self.update(db, id=admin_id, obj_in=update_data, updated_by=updated_by)
    
    def authenticate(self, db: Session, *, username: str, password: str) -> Optional[Admin]:
        """
        Authenticate admin by username and password.
        
        Args:
            db: Database session
            username: Admin username
            password: Plain password
            
        Returns:
            Optional[Admin]: Admin if authentication successful
        """
        admin = self.dao.get_by_username(db, username=username)
        if not admin:
            return None
        
        if not verify_password(password, admin.hashed_password):
            return None
        
        if not admin.is_active or admin.status != UserStatus.ACTIVE:
            return None
        
        return admin
    
    def change_password(
        self, db: Session, *, admin_id: int, password_data: PasswordChange, updated_by: Optional[int] = None
    ) -> Admin:
        """
        Change admin password.
        
        Args:
            db: Database session
            admin_id: Admin ID
            password_data: Password change data
            updated_by: User ID who updated the password
            
        Returns:
            Admin: Updated admin
            
        Raises:
            HTTPException: If old password is incorrect
        """
        admin = self.get(db, admin_id)
        
        if not verify_password(password_data.old_password, admin.hashed_password):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="旧密码不正确"
            )
        
        hashed_password = get_password_hash(password_data.new_password)
        return self.update(
            db, id=admin_id, obj_in={"hashed_password": hashed_password}, updated_by=updated_by
        )
    
    def get_active_admins(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Admin]:
        """Get active admins."""
        return self.dao.get_active_admins(db, skip=skip, limit=limit)
    
    def get_superusers(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Admin]:
        """Get superuser admins."""
        return self.dao.get_superusers(db, skip=skip, limit=limit)


class TenantService(BaseService[Tenant, TenantDAO]):
    """Service for Tenant operations."""
    
    def __init__(self):
        super().__init__(tenant_dao)
    
    def create_tenant(self, db: Session, *, tenant_in: TenantCreate, created_by: Optional[int] = None) -> Tenant:
        """
        Create a new tenant.
        
        Args:
            db: Database session
            tenant_in: Tenant creation data
            created_by: User ID who created the tenant
            
        Returns:
            Tenant: Created tenant
            
        Raises:
            HTTPException: If name, code, or email already exists
        """
        # Check if name already exists
        if self.dao.get_by_name(db, name=tenant_in.name):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="租户名称已存在"
            )
        
        # Check if code already exists
        if self.dao.get_by_code(db, code=tenant_in.code):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="租户代码已存在"
            )
        
        # Check if email already exists
        if self.dao.get_by_email(db, email=tenant_in.email):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已存在"
            )
        
        # Hash password
        hashed_password = get_password_hash(tenant_in.password)
        
        # Create tenant data
        tenant_data = tenant_in.dict(exclude={"password"})
        tenant_data["hashed_password"] = hashed_password
        tenant_data["status"] = UserStatus.ACTIVE
        
        return self.create(db, obj_in=tenant_data, created_by=created_by)
    
    def update_tenant(
        self, db: Session, *, tenant_id: int, tenant_in: TenantUpdate, updated_by: Optional[int] = None
    ) -> Tenant:
        """
        Update a tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            tenant_in: Tenant update data
            updated_by: User ID who updated the tenant
            
        Returns:
            Tenant: Updated tenant
            
        Raises:
            HTTPException: If name or email already exists
        """
        # Check if name already exists (excluding current tenant)
        if tenant_in.name:
            existing = self.dao.get_by_name(db, name=tenant_in.name)
            if existing and existing.id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="租户名称已存在"
                )
        
        # Check if email already exists (excluding current tenant)
        if tenant_in.email:
            existing = self.dao.get_by_email(db, email=tenant_in.email)
            if existing and existing.id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="邮箱已存在"
                )
        
        update_data = tenant_in.dict(exclude_unset=True)
        return self.update(db, id=tenant_id, obj_in=update_data, updated_by=updated_by)
    
    def authenticate(self, db: Session, *, code: str, password: str) -> Optional[Tenant]:
        """
        Authenticate tenant by code and password.
        
        Args:
            db: Database session
            code: Tenant code
            password: Plain password
            
        Returns:
            Optional[Tenant]: Tenant if authentication successful
        """
        tenant = self.dao.get_by_code(db, code=code)
        if not tenant:
            return None
        
        if not verify_password(password, tenant.hashed_password):
            return None
        
        if not tenant.is_active or tenant.status != UserStatus.ACTIVE:
            return None
        
        return tenant
    
    def get_active_tenants(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Tenant]:
        """Get active tenants."""
        return self.dao.get_active_tenants(db, skip=skip, limit=limit)


class UserService(BaseService[User, UserDAO]):
    """Service for User operations."""
    
    def __init__(self):
        super().__init__(user_dao)
    
    def create_user(self, db: Session, *, user_in: UserCreate, created_by: Optional[int] = None) -> User:
        """
        Create a new user.
        
        Args:
            db: Database session
            user_in: User creation data
            created_by: User ID who created the user
            
        Returns:
            User: Created user
            
        Raises:
            HTTPException: If username or email already exists in tenant
        """
        # Check if username already exists in tenant
        if self.dao.get_by_username(db, username=user_in.username, tenant_id=user_in.tenant_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名在该租户中已存在"
            )
        
        # Check if email already exists in tenant
        if self.dao.get_by_email(db, email=user_in.email, tenant_id=user_in.tenant_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱在该租户中已存在"
            )
        
        # Verify tenant exists
        tenant_service = TenantService()
        tenant_service.get(db, user_in.tenant_id)
        
        # Hash password
        hashed_password = get_password_hash(user_in.password)
        
        # Create user data
        user_data = user_in.dict(exclude={"password"})
        user_data["hashed_password"] = hashed_password
        user_data["status"] = UserStatus.ACTIVE
        
        return self.create(db, obj_in=user_data, created_by=created_by)
    
    def authenticate(self, db: Session, *, username: str, password: str, tenant_code: str) -> Optional[User]:
        """
        Authenticate user by username, password, and tenant code.
        
        Args:
            db: Database session
            username: User username
            password: Plain password
            tenant_code: Tenant code
            
        Returns:
            Optional[User]: User if authentication successful
        """
        # Get tenant by code
        tenant_service = TenantService()
        tenant = tenant_service.dao.get_by_code(db, code=tenant_code)
        if not tenant or not tenant.is_active or tenant.status != UserStatus.ACTIVE:
            return None
        
        # Get user by username and tenant
        user = self.dao.get_by_username(db, username=username, tenant_id=tenant.id)
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        if not user.is_active or user.status != UserStatus.ACTIVE:
            return None
        
        return user
    
    def get_users_by_tenant(self, db: Session, *, tenant_id: int, skip: int = 0, limit: int = 100) -> List[User]:
        """Get users by tenant."""
        return self.dao.get_by_tenant(db, tenant_id=tenant_id, skip=skip, limit=limit)
    
    def search_users(
        self, db: Session, *, query: str, tenant_id: Optional[int] = None, skip: int = 0, limit: int = 100
    ) -> List[User]:
        """Search users."""
        return self.dao.search_users(db, query=query, tenant_id=tenant_id, skip=skip, limit=limit)


# Service instances
admin_service = AdminService()
tenant_service = TenantService()
user_service = UserService()
