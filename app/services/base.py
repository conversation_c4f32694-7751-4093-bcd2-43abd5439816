"""
Base service class with common business logic patterns.
"""
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar

from fastapi import HTTPException, status
from sqlalchemy.orm import Session

from app.dao.base import BaseDAO
from app.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)
DAOType = TypeVar("DAOType", bound=BaseDAO)


class BaseService(Generic[ModelType, DAOType]):
    """Base service class with common business logic operations."""
    
    def __init__(self, dao: DAOType):
        """
        Initialize service with DAO.
        
        Args:
            dao: Data access object
        """
        self.dao = dao
    
    def create(self, db: Session, *, obj_in: Dict[str, Any], created_by: Optional[int] = None) -> ModelType:
        """
        Create a new record.
        
        Args:
            db: Database session
            obj_in: Object data
            created_by: User ID who created the record
            
        Returns:
            ModelType: Created record
            
        Raises:
            HTTPException: If creation fails
        """
        try:
            return self.dao.create(db, obj_in=obj_in, created_by=created_by)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"创建失败: {str(e)}"
            )
    
    def get(self, db: Session, id: int) -> ModelType:
        """
        Get record by ID.
        
        Args:
            db: Database session
            id: Record ID
            
        Returns:
            ModelType: Record
            
        Raises:
            HTTPException: If record not found
        """
        obj = self.dao.get(db, id)
        if not obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记录不存在"
            )
        return obj
    
    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """
        Get multiple records with pagination and filtering.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Filter conditions
            order_by: Field to order by
            order_desc: Whether to order in descending order
            
        Returns:
            List[ModelType]: List of records
        """
        return self.dao.get_multi(
            db,
            skip=skip,
            limit=limit,
            filters=filters,
            order_by=order_by,
            order_desc=order_desc
        )
    
    def update(
        self,
        db: Session,
        *,
        id: int,
        obj_in: Dict[str, Any],
        updated_by: Optional[int] = None
    ) -> ModelType:
        """
        Update a record.
        
        Args:
            db: Database session
            id: Record ID
            obj_in: Update data
            updated_by: User ID who updated the record
            
        Returns:
            ModelType: Updated record
            
        Raises:
            HTTPException: If record not found or update fails
        """
        db_obj = self.get(db, id)
        
        try:
            return self.dao.update(db, db_obj=db_obj, obj_in=obj_in, updated_by=updated_by)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"更新失败: {str(e)}"
            )
    
    def delete(self, db: Session, *, id: int) -> ModelType:
        """
        Delete a record by ID.
        
        Args:
            db: Database session
            id: Record ID
            
        Returns:
            ModelType: Deleted record
            
        Raises:
            HTTPException: If record not found
        """
        obj = self.dao.delete(db, id=id)
        if not obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="记录不存在"
            )
        return obj
    
    def count(self, db: Session, *, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count records with optional filtering.
        
        Args:
            db: Database session
            filters: Filter conditions
            
        Returns:
            int: Number of records
        """
        return self.dao.count(db, filters=filters)
    
    def exists(self, db: Session, *, filters: Dict[str, Any]) -> bool:
        """
        Check if record exists with given filters.
        
        Args:
            db: Database session
            filters: Filter conditions
            
        Returns:
            bool: True if record exists
        """
        return self.dao.exists(db, filters=filters)
    
    def get_by_field(self, db: Session, *, field: str, value: Any) -> Optional[ModelType]:
        """
        Get record by specific field value.
        
        Args:
            db: Database session
            field: Field name
            value: Field value
            
        Returns:
            Optional[ModelType]: Record if found
        """
        return self.dao.get_by_field(db, field=field, value=value)
    
    def bulk_create(self, db: Session, *, objs_in: List[Dict[str, Any]], created_by: Optional[int] = None) -> List[ModelType]:
        """
        Create multiple records in bulk.
        
        Args:
            db: Database session
            objs_in: List of object data
            created_by: User ID who created the records
            
        Returns:
            List[ModelType]: List of created records
            
        Raises:
            HTTPException: If bulk creation fails
        """
        try:
            return self.dao.bulk_create(db, objs_in=objs_in, created_by=created_by)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"批量创建失败: {str(e)}"
            )
    
    def bulk_update(
        self,
        db: Session,
        *,
        updates: List[Dict[str, Any]],
        updated_by: Optional[int] = None
    ) -> List[ModelType]:
        """
        Update multiple records in bulk.
        
        Args:
            db: Database session
            updates: List of update data (must include 'id' field)
            updated_by: User ID who updated the records
            
        Returns:
            List[ModelType]: List of updated records
            
        Raises:
            HTTPException: If bulk update fails
        """
        try:
            return self.dao.bulk_update(db, updates=updates, updated_by=updated_by)
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"批量更新失败: {str(e)}"
            )
    
    def validate_unique_field(
        self,
        db: Session,
        *,
        field: str,
        value: Any,
        exclude_id: Optional[int] = None
    ) -> None:
        """
        Validate that a field value is unique.
        
        Args:
            db: Database session
            field: Field name
            value: Field value
            exclude_id: ID to exclude from uniqueness check
            
        Raises:
            HTTPException: If field value is not unique
        """
        existing = self.get_by_field(db, field=field, value=value)
        if existing and (exclude_id is None or existing.id != exclude_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"{field} 已存在"
            )
    
    def soft_delete(self, db: Session, *, id: int, updated_by: Optional[int] = None) -> ModelType:
        """
        Soft delete a record (mark as inactive).
        
        Args:
            db: Database session
            id: Record ID
            updated_by: User ID who performed the deletion
            
        Returns:
            ModelType: Updated record
            
        Raises:
            HTTPException: If record not found or doesn't support soft delete
        """
        db_obj = self.get(db, id)
        
        if not hasattr(db_obj, 'is_active'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该记录不支持软删除"
            )
        
        return self.update(db, id=id, obj_in={"is_active": False}, updated_by=updated_by)
