"""
Redis connection and utilities.
"""
import json
from typing import Any, Optional, Union

import redis.asyncio as aioredis
from redis import Redis

from app.core.config import settings


class RedisClient:
    """Redis client wrapper with common operations."""
    
    def __init__(self):
        self._sync_client: Optional[Redis] = None
        self._async_client: Optional[aioredis.Redis] = None
    
    @property
    def sync_client(self) -> Redis:
        """Get synchronous Redis client."""
        if self._sync_client is None:
            self._sync_client = Redis.from_url(
                settings.redis_url_full,
                decode_responses=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30,
            )
        return self._sync_client
    
    @property
    def async_client(self) -> aioredis.Redis:
        """Get asynchronous Redis client."""
        if self._async_client is None:
            self._async_client = aioredis.from_url(
                settings.redis_url_full,
                decode_responses=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30,
            )
        return self._async_client
    
    async def set(
        self,
        key: str,
        value: Union[str, dict, list, int, float],
        expire: Optional[int] = None
    ) -> bool:
        """
        Set a key-value pair in Redis.
        
        Args:
            key: Redis key
            value: Value to store
            expire: Expiration time in seconds
            
        Returns:
            bool: True if successful
        """
        if isinstance(value, (dict, list)):
            value = json.dumps(value, ensure_ascii=False)
        
        result = await self.async_client.set(key, value, ex=expire)
        return bool(result)
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Get a value from Redis.
        
        Args:
            key: Redis key
            default: Default value if key doesn't exist
            
        Returns:
            Any: Value from Redis or default
        """
        value = await self.async_client.get(key)
        if value is None:
            return default
        
        # Try to parse as JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return value
    
    async def delete(self, *keys: str) -> int:
        """
        Delete keys from Redis.
        
        Args:
            keys: Keys to delete
            
        Returns:
            int: Number of keys deleted
        """
        if not keys:
            return 0
        return await self.async_client.delete(*keys)
    
    async def exists(self, key: str) -> bool:
        """
        Check if a key exists in Redis.
        
        Args:
            key: Redis key
            
        Returns:
            bool: True if key exists
        """
        return bool(await self.async_client.exists(key))
    
    async def expire(self, key: str, seconds: int) -> bool:
        """
        Set expiration time for a key.
        
        Args:
            key: Redis key
            seconds: Expiration time in seconds
            
        Returns:
            bool: True if successful
        """
        return bool(await self.async_client.expire(key, seconds))
    
    async def ttl(self, key: str) -> int:
        """
        Get time to live for a key.
        
        Args:
            key: Redis key
            
        Returns:
            int: TTL in seconds (-1 if no expiration, -2 if key doesn't exist)
        """
        return await self.async_client.ttl(key)
    
    async def incr(self, key: str, amount: int = 1) -> int:
        """
        Increment a key's value.
        
        Args:
            key: Redis key
            amount: Amount to increment
            
        Returns:
            int: New value
        """
        return await self.async_client.incr(key, amount)
    
    async def decr(self, key: str, amount: int = 1) -> int:
        """
        Decrement a key's value.
        
        Args:
            key: Redis key
            amount: Amount to decrement
            
        Returns:
            int: New value
        """
        return await self.async_client.decr(key, amount)
    
    async def hset(self, name: str, mapping: dict) -> int:
        """
        Set hash fields.
        
        Args:
            name: Hash name
            mapping: Field-value mapping
            
        Returns:
            int: Number of fields added
        """
        return await self.async_client.hset(name, mapping=mapping)
    
    async def hget(self, name: str, key: str) -> Optional[str]:
        """
        Get hash field value.
        
        Args:
            name: Hash name
            key: Field name
            
        Returns:
            Optional[str]: Field value
        """
        return await self.async_client.hget(name, key)
    
    async def hgetall(self, name: str) -> dict:
        """
        Get all hash fields and values.
        
        Args:
            name: Hash name
            
        Returns:
            dict: All fields and values
        """
        return await self.async_client.hgetall(name)
    
    async def hdel(self, name: str, *keys: str) -> int:
        """
        Delete hash fields.
        
        Args:
            name: Hash name
            keys: Field names to delete
            
        Returns:
            int: Number of fields deleted
        """
        return await self.async_client.hdel(name, *keys)
    
    async def close(self):
        """Close Redis connections."""
        if self._async_client:
            await self._async_client.close()
        if self._sync_client:
            self._sync_client.close()


# Global Redis client instance
redis_client = RedisClient()


async def get_redis() -> aioredis.Redis:
    """Get Redis client for dependency injection."""
    return redis_client.async_client
