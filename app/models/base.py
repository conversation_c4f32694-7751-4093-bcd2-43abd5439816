"""
Base model class with common fields for all models.
"""
from datetime import datetime
from typing import Optional

import arrow
from sqlalchemy import Column, DateTime, Integer, String, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import Mapped, mapped_column

Base = declarative_base()


class BaseModel(Base):
    """
    Base model class that includes common fields:
    - id: Primary key
    - created_at: Creation timestamp
    - updated_at: Last update timestamp
    - created_by: User who created the record
    - updated_by: User who last updated the record
    """
    
    __abstract__ = True
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=lambda: arrow.utcnow().datetime,
        server_default=text("CURRENT_TIMESTAMP"),
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=lambda: arrow.utcnow().datetime,
        onupdate=lambda: arrow.utcnow().datetime,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        nullable=False,
        comment="更新时间"
    )
    created_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="创建人ID"
    )
    updated_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="更新人ID"
    )
    
    def to_dict(self) -> dict:
        """Convert model instance to dictionary."""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update_from_dict(self, data: dict) -> None:
        """Update model instance from dictionary."""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(id={self.id})>"


class TimestampMixin:
    """Mixin for models that only need timestamp fields."""
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=lambda: arrow.utcnow().datetime,
        server_default=text("CURRENT_TIMESTAMP"),
        nullable=False,
        comment="创建时间"
    )
    updated_at: Mapped[datetime] = mapped_column(
        DateTime,
        default=lambda: arrow.utcnow().datetime,
        onupdate=lambda: arrow.utcnow().datetime,
        server_default=text("CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP"),
        nullable=False,
        comment="更新时间"
    )


class AuditMixin:
    """Mixin for models that need audit fields."""
    
    created_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="创建人ID"
    )
    updated_by: Mapped[Optional[int]] = mapped_column(
        Integer,
        nullable=True,
        comment="更新人ID"
    )
