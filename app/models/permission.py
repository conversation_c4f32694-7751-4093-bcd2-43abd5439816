"""
Permission and Role models for RBAC system.
"""
from enum import Enum
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Enum as SQL<PERSON><PERSON>, ForeignKey, String, Text, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class PermissionType(str, Enum):
    """Permission type enumeration."""
    CREATE = "create"
    READ = "read"
    UPDATE = "update"
    DELETE = "delete"
    MANAGE = "manage"
    EXECUTE = "execute"


class ResourceType(str, Enum):
    """Resource type enumeration."""
    USER = "user"
    TENANT = "tenant"
    ADMIN = "admin"
    PERMISSION = "permission"
    ROLE = "role"
    SYSTEM = "system"


class Permission(BaseModel):
    """
    Permission model - Defines what actions can be performed on resources.
    """
    
    __tablename__ = "permissions"
    
    name: Mapped[str] = mapped_column(
        String(100),
        unique=True,
        index=True,
        nullable=False,
        comment="权限名称"
    )
    code: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        index=True,
        nullable=False,
        comment="权限代码"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="权限描述"
    )
    resource_type: Mapped[ResourceType] = mapped_column(
        SQLEnum(ResourceType),
        nullable=False,
        comment="资源类型"
    )
    permission_type: Mapped[PermissionType] = mapped_column(
        SQLEnum(PermissionType),
        nullable=False,
        comment="权限类型"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    # Relationships
    admin_permissions: Mapped[List["AdminPermission"]] = relationship(
        "AdminPermission",
        back_populates="permission",
        cascade="all, delete-orphan"
    )
    tenant_permissions: Mapped[List["TenantPermission"]] = relationship(
        "TenantPermission",
        back_populates="permission",
        cascade="all, delete-orphan"
    )
    user_permissions: Mapped[List["UserPermission"]] = relationship(
        "UserPermission",
        back_populates="permission",
        cascade="all, delete-orphan"
    )
    role_permissions: Mapped[List["RolePermission"]] = relationship(
        "RolePermission",
        back_populates="permission",
        cascade="all, delete-orphan"
    )


class Role(BaseModel):
    """
    Role model - Groups of permissions that can be assigned to users.
    """
    
    __tablename__ = "roles"
    
    name: Mapped[str] = mapped_column(
        String(100),
        unique=True,
        index=True,
        nullable=False,
        comment="角色名称"
    )
    code: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        index=True,
        nullable=False,
        comment="角色代码"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="角色描述"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    
    # Relationships
    role_permissions: Mapped[List["RolePermission"]] = relationship(
        "RolePermission",
        back_populates="role",
        cascade="all, delete-orphan"
    )
    admin_roles: Mapped[List["AdminRole"]] = relationship(
        "AdminRole",
        back_populates="role",
        cascade="all, delete-orphan"
    )
    tenant_roles: Mapped[List["TenantRole"]] = relationship(
        "TenantRole",
        back_populates="role",
        cascade="all, delete-orphan"
    )
    user_roles: Mapped[List["UserRole"]] = relationship(
        "UserRole",
        back_populates="role",
        cascade="all, delete-orphan"
    )


# Association tables for many-to-many relationships

class RolePermission(BaseModel):
    """Association table for Role-Permission many-to-many relationship."""
    
    __tablename__ = "role_permissions"
    __table_args__ = (
        UniqueConstraint('role_id', 'permission_id', name='uq_role_permission'),
    )
    
    role_id: Mapped[int] = mapped_column(
        ForeignKey("roles.id"),
        nullable=False,
        comment="角色ID"
    )
    permission_id: Mapped[int] = mapped_column(
        ForeignKey("permissions.id"),
        nullable=False,
        comment="权限ID"
    )
    
    # Relationships
    role: Mapped["Role"] = relationship("Role", back_populates="role_permissions")
    permission: Mapped["Permission"] = relationship("Permission", back_populates="role_permissions")


class AdminPermission(BaseModel):
    """Direct permission assignment to admins."""
    
    __tablename__ = "admin_permissions"
    __table_args__ = (
        UniqueConstraint('admin_id', 'permission_id', name='uq_admin_permission'),
    )
    
    admin_id: Mapped[int] = mapped_column(
        ForeignKey("admins.id"),
        nullable=False,
        comment="管理员ID"
    )
    permission_id: Mapped[int] = mapped_column(
        ForeignKey("permissions.id"),
        nullable=False,
        comment="权限ID"
    )
    
    # Relationships
    admin: Mapped["Admin"] = relationship("Admin", back_populates="admin_permissions")
    permission: Mapped["Permission"] = relationship("Permission", back_populates="admin_permissions")


class AdminRole(BaseModel):
    """Role assignment to admins."""
    
    __tablename__ = "admin_roles"
    __table_args__ = (
        UniqueConstraint('admin_id', 'role_id', name='uq_admin_role'),
    )
    
    admin_id: Mapped[int] = mapped_column(
        ForeignKey("admins.id"),
        nullable=False,
        comment="管理员ID"
    )
    role_id: Mapped[int] = mapped_column(
        ForeignKey("roles.id"),
        nullable=False,
        comment="角色ID"
    )
    
    # Relationships
    admin: Mapped["Admin"] = relationship("Admin")
    role: Mapped["Role"] = relationship("Role", back_populates="admin_roles")


class TenantPermission(BaseModel):
    """Direct permission assignment to tenants."""
    
    __tablename__ = "tenant_permissions"
    __table_args__ = (
        UniqueConstraint('tenant_id', 'permission_id', name='uq_tenant_permission'),
    )
    
    tenant_id: Mapped[int] = mapped_column(
        ForeignKey("tenants.id"),
        nullable=False,
        comment="租户ID"
    )
    permission_id: Mapped[int] = mapped_column(
        ForeignKey("permissions.id"),
        nullable=False,
        comment="权限ID"
    )
    
    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant", back_populates="tenant_permissions")
    permission: Mapped["Permission"] = relationship("Permission", back_populates="tenant_permissions")


class TenantRole(BaseModel):
    """Role assignment to tenants."""
    
    __tablename__ = "tenant_roles"
    __table_args__ = (
        UniqueConstraint('tenant_id', 'role_id', name='uq_tenant_role'),
    )
    
    tenant_id: Mapped[int] = mapped_column(
        ForeignKey("tenants.id"),
        nullable=False,
        comment="租户ID"
    )
    role_id: Mapped[int] = mapped_column(
        ForeignKey("roles.id"),
        nullable=False,
        comment="角色ID"
    )
    
    # Relationships
    tenant: Mapped["Tenant"] = relationship("Tenant")
    role: Mapped["Role"] = relationship("Role", back_populates="tenant_roles")


class UserPermission(BaseModel):
    """Direct permission assignment to users."""
    
    __tablename__ = "user_permissions"
    __table_args__ = (
        UniqueConstraint('user_id', 'permission_id', name='uq_user_permission'),
    )
    
    user_id: Mapped[int] = mapped_column(
        ForeignKey("users.id"),
        nullable=False,
        comment="用户ID"
    )
    permission_id: Mapped[int] = mapped_column(
        ForeignKey("permissions.id"),
        nullable=False,
        comment="权限ID"
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="user_permissions")
    permission: Mapped["Permission"] = relationship("Permission", back_populates="user_permissions")


class UserRole(BaseModel):
    """Role assignment to users."""
    
    __tablename__ = "user_roles"
    __table_args__ = (
        UniqueConstraint('user_id', 'role_id', name='uq_user_role'),
    )
    
    user_id: Mapped[int] = mapped_column(
        ForeignKey("users.id"),
        nullable=False,
        comment="用户ID"
    )
    role_id: Mapped[int] = mapped_column(
        ForeignKey("roles.id"),
        nullable=False,
        comment="角色ID"
    )
    
    # Relationships
    user: Mapped["User"] = relationship("User")
    role: Mapped["Role"] = relationship("Role", back_populates="user_roles")
