"""
Event system models for publish-subscribe functionality.
"""
from enum import Enum
from typing import Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Enum as SQLE<PERSON>, JSON, String, Text
from sqlalchemy.orm import Mapped, mapped_column

from .base import BaseModel


class EventStatus(str, Enum):
    """Event status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class EventPriority(str, Enum):
    """Event priority enumeration."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


class Event(BaseModel):
    """
    Event model for the publish-subscribe system.
    """
    
    __tablename__ = "events"
    
    name: Mapped[str] = mapped_column(
        String(100),
        index=True,
        nullable=False,
        comment="事件名称"
    )
    event_type: Mapped[str] = mapped_column(
        String(50),
        index=True,
        nullable=False,
        comment="事件类型"
    )
    source: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="事件源"
    )
    payload: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="事件数据"
    )
    status: Mapped[EventStatus] = mapped_column(
        SQLEnum(EventStatus),
        default=EventStatus.PENDING,
        nullable=False,
        index=True,
        comment="事件状态"
    )
    priority: Mapped[EventPriority] = mapped_column(
        SQLEnum(EventPriority),
        default=EventPriority.NORMAL,
        nullable=False,
        comment="事件优先级"
    )
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    retry_count: Mapped[int] = mapped_column(
        default=0,
        nullable=False,
        comment="重试次数"
    )
    max_retries: Mapped[int] = mapped_column(
        default=3,
        nullable=False,
        comment="最大重试次数"
    )
    
    def __repr__(self) -> str:
        return f"<Event(id={self.id}, name={self.name}, type={self.event_type}, status={self.status})>"


class EventSubscription(BaseModel):
    """
    Event subscription model for managing event handlers.
    """
    
    __tablename__ = "event_subscriptions"
    
    event_type: Mapped[str] = mapped_column(
        String(50),
        index=True,
        nullable=False,
        comment="订阅的事件类型"
    )
    handler_name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="处理器名称"
    )
    handler_module: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="处理器模块路径"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    priority: Mapped[int] = mapped_column(
        default=0,
        nullable=False,
        comment="处理优先级"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="订阅描述"
    )
    
    def __repr__(self) -> str:
        return f"<EventSubscription(id={self.id}, event_type={self.event_type}, handler={self.handler_name})>"


class EventLog(BaseModel):
    """
    Event processing log for audit and debugging.
    """
    
    __tablename__ = "event_logs"
    
    event_id: Mapped[int] = mapped_column(
        nullable=False,
        index=True,
        comment="事件ID"
    )
    handler_name: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="处理器名称"
    )
    status: Mapped[EventStatus] = mapped_column(
        SQLEnum(EventStatus),
        nullable=False,
        comment="处理状态"
    )
    execution_time: Mapped[Optional[float]] = mapped_column(
        nullable=True,
        comment="执行时间(秒)"
    )
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="错误信息"
    )
    result: Mapped[Optional[dict]] = mapped_column(
        JSON,
        nullable=True,
        comment="处理结果"
    )
    
    def __repr__(self) -> str:
        return f"<EventLog(id={self.id}, event_id={self.event_id}, handler={self.handler_name}, status={self.status})>"
