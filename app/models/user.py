"""
User related models: <PERSON><PERSON>, Tenant, User
"""
from enum import Enum
from typing import List, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, Enum as S<PERSON><PERSON><PERSON>, ForeignKey, String, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class UserStatus(str, Enum):
    """User status enumeration."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    DELETED = "deleted"


class UserRole(str, Enum):
    """User role enumeration."""
    ADMIN = "admin"
    TENANT = "tenant"
    USER = "user"


class Admin(BaseModel):
    """
    Admin model - Super administrators who can manage tenants and users.
    """
    
    __tablename__ = "admins"
    
    username: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        index=True,
        nullable=False,
        comment="管理员用户名"
    )
    email: Mapped[str] = mapped_column(
        String(100),
        unique=True,
        index=True,
        nullable=False,
        comment="邮箱地址"
    )
    hashed_password: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="加密密码"
    )
    full_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="全名"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    is_superuser: Mapped[bool] = mapped_column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否超级管理员"
    )
    status: Mapped[UserStatus] = mapped_column(
        SQLEnum(UserStatus),
        default=UserStatus.ACTIVE,
        nullable=False,
        comment="用户状态"
    )
    
    # Relationships
    admin_permissions: Mapped[List["AdminPermission"]] = relationship(
        "AdminPermission",
        back_populates="admin",
        cascade="all, delete-orphan"
    )


class Tenant(BaseModel):
    """
    Tenant model - Organizations that can manage their own users.
    """
    
    __tablename__ = "tenants"
    
    name: Mapped[str] = mapped_column(
        String(100),
        unique=True,
        index=True,
        nullable=False,
        comment="租户名称"
    )
    code: Mapped[str] = mapped_column(
        String(50),
        unique=True,
        index=True,
        nullable=False,
        comment="租户代码"
    )
    email: Mapped[str] = mapped_column(
        String(100),
        unique=True,
        index=True,
        nullable=False,
        comment="租户邮箱"
    )
    hashed_password: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="加密密码"
    )
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        nullable=True,
        comment="租户描述"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    status: Mapped[UserStatus] = mapped_column(
        SQLEnum(UserStatus),
        default=UserStatus.ACTIVE,
        nullable=False,
        comment="租户状态"
    )
    
    # Relationships
    users: Mapped[List["User"]] = relationship(
        "User",
        back_populates="tenant",
        cascade="all, delete-orphan"
    )
    tenant_permissions: Mapped[List["TenantPermission"]] = relationship(
        "TenantPermission",
        back_populates="tenant",
        cascade="all, delete-orphan"
    )


class User(BaseModel):
    """
    User model - Regular users belonging to tenants.
    """
    
    __tablename__ = "users"
    
    username: Mapped[str] = mapped_column(
        String(50),
        index=True,
        nullable=False,
        comment="用户名"
    )
    email: Mapped[str] = mapped_column(
        String(100),
        index=True,
        nullable=False,
        comment="邮箱地址"
    )
    hashed_password: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        comment="加密密码"
    )
    full_name: Mapped[Optional[str]] = mapped_column(
        String(100),
        nullable=True,
        comment="全名"
    )
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否激活"
    )
    status: Mapped[UserStatus] = mapped_column(
        SQLEnum(UserStatus),
        default=UserStatus.ACTIVE,
        nullable=False,
        comment="用户状态"
    )
    
    # Foreign Keys
    tenant_id: Mapped[int] = mapped_column(
        ForeignKey("tenants.id"),
        nullable=False,
        index=True,
        comment="所属租户ID"
    )
    
    # Relationships
    tenant: Mapped["Tenant"] = relationship(
        "Tenant",
        back_populates="users"
    )
    user_permissions: Mapped[List["UserPermission"]] = relationship(
        "UserPermission",
        back_populates="user",
        cascade="all, delete-orphan"
    )
