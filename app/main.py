"""
FastAPI application entry point.
"""
import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from app.api.auth import router as auth_router
from app.api.users import router as users_router
from app.api.tenants import router as tenants_router
from app.api.admins import router as admins_router
from app.core.config import settings
from app.core.logging import setup_logging, get_logger, LogContext
from app.db.database import engine
from app.events.base import event_bus
from app.handlers import user_handlers  # Import to register handlers

logger = get_logger("main")


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging HTTP requests."""
    
    async def dispatch(self, request: Request, call_next):
        """Process request and log details."""
        import time
        import uuid
        
        # Generate request ID
        request_id = str(uuid.uuid4())
        
        # Start time
        start_time = time.time()
        
        # Log request
        with LogContext(
            request_id=request_id,
            ip_address=request.client.host,
            method=request.method,
            path=request.url.path
        ):
            logger.info(f"Request started: {request.method} {request.url.path}")
            
            # Process request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Log response
            logger.info(
                f"Request completed: {request.method} {request.url.path} "
                f"- Status: {response.status_code} - Duration: {duration:.3f}s"
            )
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """Middleware for adding security headers."""
    
    async def dispatch(self, request: Request, call_next):
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        if not settings.debug:
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        return response


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events."""
    # Startup
    logger.info("Starting up application...")
    
    # Setup logging
    setup_logging()
    
    # Database is ready (tables created via Alembic migrations)
    
    # Event bus is ready (handlers are auto-registered via decorators)
    
    # Publish startup event (skip if database not available)
    try:
        await event_bus.publish(
            event_type="app.startup",
            data={
                "app_name": settings.app_name,
                "version": settings.app_version,
                "environment": settings.environment
            }
        )
        logger.info("Startup event published successfully")
    except Exception as e:
        logger.warning(f"Failed to publish startup event: {e}")
    
    logger.info("Application startup completed")
    
    yield
    
    # Shutdown
    logger.info("Shutting down application...")
    
    # Publish shutdown event
    event_bus.publish(
        event_type="app.shutdown",
        data={
            "app_name": settings.app_name,
            "version": settings.app_version
        }
    )
    
    # Publish shutdown event (skip if database not available)
    try:
        await event_bus.publish(
            event_type="app.shutdown",
            data={"app_name": settings.app_name}
        )
        logger.info("Shutdown event published successfully")
    except Exception as e:
        logger.warning(f"Failed to publish shutdown event: {e}")
    
    logger.info("Application shutdown completed")


# Create FastAPI application
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Multi-tenant FastAPI application with RBAC",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    openapi_url="/openapi.json" if settings.debug else None,
    lifespan=lifespan
)

# Add middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=settings.cors_allow_credentials,
    allow_methods=settings.cors_allow_methods,
    allow_headers=settings.cors_allow_headers,
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"] if settings.debug else ["localhost", "127.0.0.1"]
)

app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(SecurityHeadersMiddleware)

# Add routers
app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
app.include_router(users_router, prefix="/api/v1/users", tags=["Users"])
app.include_router(tenants_router, prefix="/api/v1/tenants", tags=["Tenants"])
app.include_router(admins_router, prefix="/api/v1/admins", tags=["Admins"])


# Exception handlers
@app.exception_handler(ValueError)
async def value_error_handler(request: Request, exc: ValueError):
    """Handle ValueError exceptions."""
    logger.error(f"ValueError: {exc}")
    return JSONResponse(
        status_code=400,
        content={"detail": str(exc)}
    )


@app.exception_handler(PermissionError)
async def permission_error_handler(request: Request, exc: PermissionError):
    """Handle PermissionError exceptions."""
    logger.error(f"PermissionError: {exc}")
    return JSONResponse(
        status_code=403,
        content={"detail": "Permission denied"}
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.exception(f"Unhandled exception: {exc}")
    
    if settings.debug:
        return JSONResponse(
            status_code=500,
            content={"detail": str(exc)}
        )
    else:
        return JSONResponse(
            status_code=500,
            content={"detail": "Internal server error"}
        )


# Health check endpoints
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment
    }


@app.get("/health/detailed", tags=["Health"])
async def detailed_health_check():
    """Detailed health check endpoint."""
    from app.db.database import engine
    from app.db.redis_client import redis_client
    
    health_status = {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "environment": settings.environment,
        "checks": {}
    }
    
    # Database check
    try:
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        health_status["checks"]["database"] = "healthy"
    except Exception as e:
        health_status["checks"]["database"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"
    
    # Redis check
    try:
        await redis_client.ping()
        health_status["checks"]["redis"] = "healthy"
    except Exception as e:
        health_status["checks"]["redis"] = f"unhealthy: {str(e)}"
        health_status["status"] = "unhealthy"
    
    # Event bus check
    try:
        # Check if event bus has handlers registered
        handler_count = len(event_bus._handlers) + len(event_bus._global_handlers)
        health_status["checks"]["event_bus"] = f"healthy ({handler_count} handlers)"
    except Exception as e:
        health_status["checks"]["event_bus"] = f"unhealthy: {str(e)}"
    
    return health_status


@app.get("/", tags=["Root"])
async def root():
    """Root endpoint."""
    return {
        "message": f"Welcome to {settings.app_name}",
        "version": settings.app_version,
        "docs_url": "/docs" if settings.debug else None
    }


# Metrics endpoint (for monitoring)
@app.get("/metrics", tags=["Monitoring"])
async def metrics():
    """Basic metrics endpoint."""
    from app.db.database import SessionLocal
    from app.models.user import Admin, Tenant, User
    
    try:
        db = SessionLocal()
        
        metrics = {
            "timestamp": "2024-01-01T00:00:00Z",  # Replace with actual timestamp
            "counters": {
                "total_admins": db.query(Admin).count(),
                "total_tenants": db.query(Tenant).count(),
                "total_users": db.query(User).count(),
                "active_users": db.query(User).filter(User.is_active == True).count(),
            },
            "gauges": {
                "app_version": settings.app_version,
                "environment": settings.environment
            }
        }
        
        return metrics
        
    except Exception as e:
        logger.error(f"Failed to generate metrics: {e}")
        return {"error": "Failed to generate metrics"}
    finally:
        db.close()


if __name__ == "__main__":
    import uvicorn
    
    # Run the application
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True,
        use_colors=settings.log_colored
    )
