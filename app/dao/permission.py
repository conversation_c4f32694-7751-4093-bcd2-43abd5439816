"""
Permission-related DAO classes.
"""
from typing import List, Optional

from sqlalchemy.orm import Session

from app.dao.base import BaseDAO
from app.models.permission import (
    AdminPermission,
    AdminRole,
    Permission,
    PermissionType,
    ResourceType,
    Role,
    RolePermission,
    TenantPermission,
    TenantRole,
    UserPermission,
    UserRole,
)


class PermissionDAO(BaseDAO[Permission]):
    """DAO for Permission model."""
    
    def __init__(self):
        super().__init__(Permission)
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[Permission]:
        """
        Get permission by code.
        
        Args:
            db: Database session
            code: Permission code
            
        Returns:
            Optional[Permission]: Permission if found
        """
        return db.query(Permission).filter(Permission.code == code).first()
    
    def get_by_resource_type(
        self, db: Session, *, resource_type: ResourceType, skip: int = 0, limit: int = 100
    ) -> List[Permission]:
        """
        Get permissions by resource type.
        
        Args:
            db: Database session
            resource_type: Resource type
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Permission]: List of permissions
        """
        return (
            db.query(Permission)
            .filter(Permission.resource_type == resource_type)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_permission_type(
        self, db: Session, *, permission_type: PermissionType, skip: int = 0, limit: int = 100
    ) -> List[Permission]:
        """
        Get permissions by permission type.
        
        Args:
            db: Database session
            permission_type: Permission type
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Permission]: List of permissions
        """
        return (
            db.query(Permission)
            .filter(Permission.permission_type == permission_type)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_active_permissions(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Permission]:
        """
        Get active permissions.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Permission]: List of active permissions
        """
        return (
            db.query(Permission)
            .filter(Permission.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )


class RoleDAO(BaseDAO[Role]):
    """DAO for Role model."""
    
    def __init__(self):
        super().__init__(Role)
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[Role]:
        """
        Get role by code.
        
        Args:
            db: Database session
            code: Role code
            
        Returns:
            Optional[Role]: Role if found
        """
        return db.query(Role).filter(Role.code == code).first()
    
    def get_active_roles(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Role]:
        """
        Get active roles.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Role]: List of active roles
        """
        return (
            db.query(Role)
            .filter(Role.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_role_permissions(self, db: Session, *, role_id: int) -> List[Permission]:
        """
        Get permissions for a role.
        
        Args:
            db: Database session
            role_id: Role ID
            
        Returns:
            List[Permission]: List of permissions
        """
        return (
            db.query(Permission)
            .join(RolePermission)
            .filter(RolePermission.role_id == role_id, Permission.is_active == True)
            .all()
        )


class RolePermissionDAO(BaseDAO[RolePermission]):
    """DAO for RolePermission model."""
    
    def __init__(self):
        super().__init__(RolePermission)
    
    def assign_permission_to_role(
        self, db: Session, *, role_id: int, permission_id: int, created_by: Optional[int] = None
    ) -> Optional[RolePermission]:
        """
        Assign permission to role.
        
        Args:
            db: Database session
            role_id: Role ID
            permission_id: Permission ID
            created_by: User ID who created the assignment
            
        Returns:
            Optional[RolePermission]: Assignment if successful
        """
        # Check if assignment already exists
        existing = (
            db.query(RolePermission)
            .filter(RolePermission.role_id == role_id, RolePermission.permission_id == permission_id)
            .first()
        )
        
        if existing:
            return existing
        
        assignment = RolePermission(
            role_id=role_id,
            permission_id=permission_id,
            created_by=created_by,
            updated_by=created_by
        )
        db.add(assignment)
        db.commit()
        db.refresh(assignment)
        return assignment
    
    def remove_permission_from_role(self, db: Session, *, role_id: int, permission_id: int) -> bool:
        """
        Remove permission from role.
        
        Args:
            db: Database session
            role_id: Role ID
            permission_id: Permission ID
            
        Returns:
            bool: True if removed successfully
        """
        assignment = (
            db.query(RolePermission)
            .filter(RolePermission.role_id == role_id, RolePermission.permission_id == permission_id)
            .first()
        )
        
        if assignment:
            db.delete(assignment)
            db.commit()
            return True
        
        return False


class AdminPermissionDAO(BaseDAO[AdminPermission]):
    """DAO for AdminPermission model."""
    
    def __init__(self):
        super().__init__(AdminPermission)
    
    def get_admin_permissions(self, db: Session, *, admin_id: int) -> List[Permission]:
        """
        Get direct permissions for an admin.
        
        Args:
            db: Database session
            admin_id: Admin ID
            
        Returns:
            List[Permission]: List of permissions
        """
        return (
            db.query(Permission)
            .join(AdminPermission)
            .filter(AdminPermission.admin_id == admin_id, Permission.is_active == True)
            .all()
        )


class AdminRoleDAO(BaseDAO[AdminRole]):
    """DAO for AdminRole model."""
    
    def __init__(self):
        super().__init__(AdminRole)
    
    def get_admin_roles(self, db: Session, *, admin_id: int) -> List[Role]:
        """
        Get roles for an admin.
        
        Args:
            db: Database session
            admin_id: Admin ID
            
        Returns:
            List[Role]: List of roles
        """
        return (
            db.query(Role)
            .join(AdminRole)
            .filter(AdminRole.admin_id == admin_id, Role.is_active == True)
            .all()
        )


class TenantPermissionDAO(BaseDAO[TenantPermission]):
    """DAO for TenantPermission model."""
    
    def __init__(self):
        super().__init__(TenantPermission)
    
    def get_tenant_permissions(self, db: Session, *, tenant_id: int) -> List[Permission]:
        """
        Get direct permissions for a tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            
        Returns:
            List[Permission]: List of permissions
        """
        return (
            db.query(Permission)
            .join(TenantPermission)
            .filter(TenantPermission.tenant_id == tenant_id, Permission.is_active == True)
            .all()
        )


class TenantRoleDAO(BaseDAO[TenantRole]):
    """DAO for TenantRole model."""
    
    def __init__(self):
        super().__init__(TenantRole)
    
    def get_tenant_roles(self, db: Session, *, tenant_id: int) -> List[Role]:
        """
        Get roles for a tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            
        Returns:
            List[Role]: List of roles
        """
        return (
            db.query(Role)
            .join(TenantRole)
            .filter(TenantRole.tenant_id == tenant_id, Role.is_active == True)
            .all()
        )


class UserPermissionDAO(BaseDAO[UserPermission]):
    """DAO for UserPermission model."""
    
    def __init__(self):
        super().__init__(UserPermission)
    
    def get_user_permissions(self, db: Session, *, user_id: int) -> List[Permission]:
        """
        Get direct permissions for a user.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            List[Permission]: List of permissions
        """
        return (
            db.query(Permission)
            .join(UserPermission)
            .filter(UserPermission.user_id == user_id, Permission.is_active == True)
            .all()
        )


class UserRoleDAO(BaseDAO[UserRole]):
    """DAO for UserRole model."""
    
    def __init__(self):
        super().__init__(UserRole)
    
    def get_user_roles(self, db: Session, *, user_id: int) -> List[Role]:
        """
        Get roles for a user.
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            List[Role]: List of roles
        """
        return (
            db.query(Role)
            .join(UserRole)
            .filter(UserRole.user_id == user_id, Role.is_active == True)
            .all()
        )


# DAO instances
permission_dao = PermissionDAO()
role_dao = RoleDAO()
role_permission_dao = RolePermissionDAO()
admin_permission_dao = AdminPermissionDAO()
admin_role_dao = AdminRoleDAO()
tenant_permission_dao = TenantPermissionDAO()
tenant_role_dao = TenantRoleDAO()
user_permission_dao = UserPermissionDAO()
user_role_dao = UserRoleDAO()
