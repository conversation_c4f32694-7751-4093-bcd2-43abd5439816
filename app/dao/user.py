"""
User-related DAO classes.
"""
from typing import List, Optional

from sqlalchemy.orm import Session

from app.dao.base import BaseDAO
from app.models.user import Admin, Tenant, User, UserStatus


class AdminDAO(BaseDAO[Admin]):
    """DAO for Admin model."""
    
    def __init__(self):
        super().__init__(Admin)
    
    def get_by_username(self, db: Session, *, username: str) -> Optional[Admin]:
        """
        Get admin by username.
        
        Args:
            db: Database session
            username: Admin username
            
        Returns:
            Optional[Admin]: Admin if found
        """
        return db.query(Admin).filter(Admin.username == username).first()
    
    def get_by_email(self, db: Session, *, email: str) -> Optional[Admin]:
        """
        Get admin by email.
        
        Args:
            db: Database session
            email: Admin email
            
        Returns:
            Optional[Admin]: Admin if found
        """
        return db.query(Admin).filter(Admin.email == email).first()
    
    def get_active_admins(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Admin]:
        """
        Get active admins.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Admin]: List of active admins
        """
        return (
            db.query(Admin)
            .filter(Admin.is_active == True, Admin.status == UserStatus.ACTIVE)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_superusers(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Admin]:
        """
        Get superuser admins.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Admin]: List of superuser admins
        """
        return (
            db.query(Admin)
            .filter(Admin.is_superuser == True, Admin.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )


class TenantDAO(BaseDAO[Tenant]):
    """DAO for Tenant model."""
    
    def __init__(self):
        super().__init__(Tenant)
    
    def get_by_name(self, db: Session, *, name: str) -> Optional[Tenant]:
        """
        Get tenant by name.
        
        Args:
            db: Database session
            name: Tenant name
            
        Returns:
            Optional[Tenant]: Tenant if found
        """
        return db.query(Tenant).filter(Tenant.name == name).first()
    
    def get_by_code(self, db: Session, *, code: str) -> Optional[Tenant]:
        """
        Get tenant by code.
        
        Args:
            db: Database session
            code: Tenant code
            
        Returns:
            Optional[Tenant]: Tenant if found
        """
        return db.query(Tenant).filter(Tenant.code == code).first()
    
    def get_by_email(self, db: Session, *, email: str) -> Optional[Tenant]:
        """
        Get tenant by email.
        
        Args:
            db: Database session
            email: Tenant email
            
        Returns:
            Optional[Tenant]: Tenant if found
        """
        return db.query(Tenant).filter(Tenant.email == email).first()
    
    def get_active_tenants(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Tenant]:
        """
        Get active tenants.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Tenant]: List of active tenants
        """
        return (
            db.query(Tenant)
            .filter(Tenant.is_active == True, Tenant.status == UserStatus.ACTIVE)
            .offset(skip)
            .limit(limit)
            .all()
        )


class UserDAO(BaseDAO[User]):
    """DAO for User model."""
    
    def __init__(self):
        super().__init__(User)
    
    def get_by_username(self, db: Session, *, username: str, tenant_id: Optional[int] = None) -> Optional[User]:
        """
        Get user by username.
        
        Args:
            db: Database session
            username: User username
            tenant_id: Tenant ID for multi-tenant filtering
            
        Returns:
            Optional[User]: User if found
        """
        query = db.query(User).filter(User.username == username)
        if tenant_id is not None:
            query = query.filter(User.tenant_id == tenant_id)
        return query.first()
    
    def get_by_email(self, db: Session, *, email: str, tenant_id: Optional[int] = None) -> Optional[User]:
        """
        Get user by email.
        
        Args:
            db: Database session
            email: User email
            tenant_id: Tenant ID for multi-tenant filtering
            
        Returns:
            Optional[User]: User if found
        """
        query = db.query(User).filter(User.email == email)
        if tenant_id is not None:
            query = query.filter(User.tenant_id == tenant_id)
        return query.first()
    
    def get_by_tenant(self, db: Session, *, tenant_id: int, skip: int = 0, limit: int = 100) -> List[User]:
        """
        Get users by tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[User]: List of users in tenant
        """
        return (
            db.query(User)
            .filter(User.tenant_id == tenant_id)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_active_users_by_tenant(
        self, db: Session, *, tenant_id: int, skip: int = 0, limit: int = 100
    ) -> List[User]:
        """
        Get active users by tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[User]: List of active users in tenant
        """
        return (
            db.query(User)
            .filter(
                User.tenant_id == tenant_id,
                User.is_active == True,
                User.status == UserStatus.ACTIVE
            )
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def count_by_tenant(self, db: Session, *, tenant_id: int) -> int:
        """
        Count users by tenant.
        
        Args:
            db: Database session
            tenant_id: Tenant ID
            
        Returns:
            int: Number of users in tenant
        """
        return db.query(User).filter(User.tenant_id == tenant_id).count()
    
    def search_users(
        self,
        db: Session,
        *,
        query: str,
        tenant_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[User]:
        """
        Search users by username, email, or full name.
        
        Args:
            db: Database session
            query: Search query
            tenant_id: Tenant ID for multi-tenant filtering
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[User]: List of matching users
        """
        search_filter = (
            User.username.ilike(f"%{query}%") |
            User.email.ilike(f"%{query}%") |
            User.full_name.ilike(f"%{query}%")
        )
        
        db_query = db.query(User).filter(search_filter)
        
        if tenant_id is not None:
            db_query = db_query.filter(User.tenant_id == tenant_id)
        
        return db_query.offset(skip).limit(limit).all()


# DAO instances
admin_dao = AdminDAO()
tenant_dao = TenantDAO()
user_dao = UserDAO()
