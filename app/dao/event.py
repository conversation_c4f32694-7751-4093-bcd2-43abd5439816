"""
Event-related DAO classes.
"""
from typing import List, Optional

from sqlalchemy.orm import Session

from app.dao.base import BaseDAO
from app.models.event import Event, EventLog, EventPriority, EventStatus, EventSubscription


class EventDAO(BaseDAO[Event]):
    """DAO for Event model."""
    
    def __init__(self):
        super().__init__(Event)
    
    def get_by_status(
        self, db: Session, *, status: EventStatus, skip: int = 0, limit: int = 100
    ) -> List[Event]:
        """
        Get events by status.
        
        Args:
            db: Database session
            status: Event status
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Event]: List of events
        """
        return (
            db.query(Event)
            .filter(Event.status == status)
            .order_by(Event.created_at)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_type(
        self, db: Session, *, event_type: str, skip: int = 0, limit: int = 100
    ) -> List[Event]:
        """
        Get events by type.
        
        Args:
            db: Database session
            event_type: Event type
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Event]: List of events
        """
        return (
            db.query(Event)
            .filter(Event.event_type == event_type)
            .order_by(Event.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_pending_events(self, db: Session, *, limit: int = 100) -> List[Event]:
        """
        Get pending events ordered by priority and creation time.
        
        Args:
            db: Database session
            limit: Maximum number of records to return
            
        Returns:
            List[Event]: List of pending events
        """
        priority_order = {
            EventPriority.CRITICAL: 1,
            EventPriority.HIGH: 2,
            EventPriority.NORMAL: 3,
            EventPriority.LOW: 4,
        }
        
        return (
            db.query(Event)
            .filter(Event.status == EventStatus.PENDING)
            .order_by(
                Event.priority.case(priority_order),
                Event.created_at
            )
            .limit(limit)
            .all()
        )
    
    def get_failed_events(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Event]:
        """
        Get failed events that can be retried.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[Event]: List of failed events
        """
        return (
            db.query(Event)
            .filter(
                Event.status == EventStatus.FAILED,
                Event.retry_count < Event.max_retries
            )
            .order_by(Event.created_at)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def update_status(
        self,
        db: Session,
        *,
        event_id: int,
        status: EventStatus,
        error_message: Optional[str] = None,
        increment_retry: bool = False
    ) -> Optional[Event]:
        """
        Update event status.
        
        Args:
            db: Database session
            event_id: Event ID
            status: New status
            error_message: Error message if failed
            increment_retry: Whether to increment retry count
            
        Returns:
            Optional[Event]: Updated event
        """
        event = self.get(db, event_id)
        if not event:
            return None
        
        event.status = status
        if error_message:
            event.error_message = error_message
        if increment_retry:
            event.retry_count += 1
        
        db.commit()
        db.refresh(event)
        return event


class EventSubscriptionDAO(BaseDAO[EventSubscription]):
    """DAO for EventSubscription model."""
    
    def __init__(self):
        super().__init__(EventSubscription)
    
    def get_by_event_type(self, db: Session, *, event_type: str) -> List[EventSubscription]:
        """
        Get active subscriptions for an event type.
        
        Args:
            db: Database session
            event_type: Event type
            
        Returns:
            List[EventSubscription]: List of subscriptions
        """
        return (
            db.query(EventSubscription)
            .filter(
                EventSubscription.event_type == event_type,
                EventSubscription.is_active == True
            )
            .order_by(EventSubscription.priority.desc())
            .all()
        )
    
    def get_active_subscriptions(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[EventSubscription]:
        """
        Get all active subscriptions.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[EventSubscription]: List of active subscriptions
        """
        return (
            db.query(EventSubscription)
            .filter(EventSubscription.is_active == True)
            .order_by(EventSubscription.event_type, EventSubscription.priority.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_handler(self, db: Session, *, handler_name: str) -> List[EventSubscription]:
        """
        Get subscriptions by handler name.
        
        Args:
            db: Database session
            handler_name: Handler name
            
        Returns:
            List[EventSubscription]: List of subscriptions
        """
        return (
            db.query(EventSubscription)
            .filter(EventSubscription.handler_name == handler_name)
            .all()
        )


class EventLogDAO(BaseDAO[EventLog]):
    """DAO for EventLog model."""
    
    def __init__(self):
        super().__init__(EventLog)
    
    def get_by_event(self, db: Session, *, event_id: int, skip: int = 0, limit: int = 100) -> List[EventLog]:
        """
        Get logs for an event.
        
        Args:
            db: Database session
            event_id: Event ID
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[EventLog]: List of event logs
        """
        return (
            db.query(EventLog)
            .filter(EventLog.event_id == event_id)
            .order_by(EventLog.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_handler(
        self, db: Session, *, handler_name: str, skip: int = 0, limit: int = 100
    ) -> List[EventLog]:
        """
        Get logs by handler name.
        
        Args:
            db: Database session
            handler_name: Handler name
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[EventLog]: List of event logs
        """
        return (
            db.query(EventLog)
            .filter(EventLog.handler_name == handler_name)
            .order_by(EventLog.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_by_status(
        self, db: Session, *, status: EventStatus, skip: int = 0, limit: int = 100
    ) -> List[EventLog]:
        """
        Get logs by status.
        
        Args:
            db: Database session
            status: Event status
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[EventLog]: List of event logs
        """
        return (
            db.query(EventLog)
            .filter(EventLog.status == status)
            .order_by(EventLog.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def create_log(
        self,
        db: Session,
        *,
        event_id: int,
        handler_name: str,
        status: EventStatus,
        execution_time: Optional[float] = None,
        error_message: Optional[str] = None,
        result: Optional[dict] = None,
        created_by: Optional[int] = None
    ) -> EventLog:
        """
        Create an event log entry.
        
        Args:
            db: Database session
            event_id: Event ID
            handler_name: Handler name
            status: Processing status
            execution_time: Execution time in seconds
            error_message: Error message if failed
            result: Processing result
            created_by: User ID who created the log
            
        Returns:
            EventLog: Created log entry
        """
        log_data = {
            "event_id": event_id,
            "handler_name": handler_name,
            "status": status,
            "execution_time": execution_time,
            "error_message": error_message,
            "result": result,
        }
        
        return self.create(db, obj_in=log_data, created_by=created_by)


# DAO instances
event_dao = EventDAO()
event_subscription_dao = EventSubscriptionDAO()
event_log_dao = EventLogDAO()
