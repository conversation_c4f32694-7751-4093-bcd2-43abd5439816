"""
Base DAO class with common database operations.
"""
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union

from sqlalchemy import and_, desc, func
from sqlalchemy.orm import Session

from app.models.base import BaseModel

ModelType = TypeVar("ModelType", bound=BaseModel)


class BaseDAO(Generic[ModelType]):
    """Base DAO class with common CRUD operations."""
    
    def __init__(self, model: Type[ModelType]):
        """
        Initialize DAO with model class.
        
        Args:
            model: SQLAlchemy model class
        """
        self.model = model
    
    def create(self, db: Session, *, obj_in: Dict[str, Any], created_by: Optional[int] = None) -> ModelType:
        """
        Create a new record.
        
        Args:
            db: Database session
            obj_in: Object data
            created_by: User ID who created the record
            
        Returns:
            ModelType: Created record
        """
        if created_by is not None:
            obj_in["created_by"] = created_by
            obj_in["updated_by"] = created_by
        
        db_obj = self.model(**obj_in)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get(self, db: Session, id: int) -> Optional[ModelType]:
        """
        Get record by ID.
        
        Args:
            db: Database session
            id: Record ID
            
        Returns:
            Optional[ModelType]: Record if found
        """
        return db.query(self.model).filter(self.model.id == id).first()
    
    def get_multi(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        order_by: Optional[str] = None,
        order_desc: bool = False
    ) -> List[ModelType]:
        """
        Get multiple records with pagination and filtering.
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Filter conditions
            order_by: Field to order by
            order_desc: Whether to order in descending order
            
        Returns:
            List[ModelType]: List of records
        """
        query = db.query(self.model)
        
        # Apply filters
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key):
                    if isinstance(value, list):
                        query = query.filter(getattr(self.model, key).in_(value))
                    else:
                        query = query.filter(getattr(self.model, key) == value)
        
        # Apply ordering
        if order_by and hasattr(self.model, order_by):
            order_field = getattr(self.model, order_by)
            if order_desc:
                query = query.order_by(desc(order_field))
            else:
                query = query.order_by(order_field)
        
        return query.offset(skip).limit(limit).all()
    
    def update(
        self,
        db: Session,
        *,
        db_obj: ModelType,
        obj_in: Dict[str, Any],
        updated_by: Optional[int] = None
    ) -> ModelType:
        """
        Update a record.
        
        Args:
            db: Database session
            db_obj: Database object to update
            obj_in: Update data
            updated_by: User ID who updated the record
            
        Returns:
            ModelType: Updated record
        """
        if updated_by is not None:
            obj_in["updated_by"] = updated_by
        
        for field, value in obj_in.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)
        
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def delete(self, db: Session, *, id: int) -> Optional[ModelType]:
        """
        Delete a record by ID.
        
        Args:
            db: Database session
            id: Record ID
            
        Returns:
            Optional[ModelType]: Deleted record if found
        """
        obj = db.query(self.model).get(id)
        if obj:
            db.delete(obj)
            db.commit()
        return obj
    
    def count(self, db: Session, *, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count records with optional filtering.
        
        Args:
            db: Database session
            filters: Filter conditions
            
        Returns:
            int: Number of records
        """
        query = db.query(func.count(self.model.id))
        
        # Apply filters
        if filters:
            for key, value in filters.items():
                if hasattr(self.model, key):
                    if isinstance(value, list):
                        query = query.filter(getattr(self.model, key).in_(value))
                    else:
                        query = query.filter(getattr(self.model, key) == value)
        
        return query.scalar()
    
    def exists(self, db: Session, *, filters: Dict[str, Any]) -> bool:
        """
        Check if record exists with given filters.
        
        Args:
            db: Database session
            filters: Filter conditions
            
        Returns:
            bool: True if record exists
        """
        query = db.query(self.model)
        
        for key, value in filters.items():
            if hasattr(self.model, key):
                query = query.filter(getattr(self.model, key) == value)
        
        return query.first() is not None
    
    def get_by_field(self, db: Session, *, field: str, value: Any) -> Optional[ModelType]:
        """
        Get record by specific field value.
        
        Args:
            db: Database session
            field: Field name
            value: Field value
            
        Returns:
            Optional[ModelType]: Record if found
        """
        if not hasattr(self.model, field):
            return None
        
        return db.query(self.model).filter(getattr(self.model, field) == value).first()
    
    def get_multi_by_field(
        self,
        db: Session,
        *,
        field: str,
        values: List[Any],
        skip: int = 0,
        limit: int = 100
    ) -> List[ModelType]:
        """
        Get multiple records by field values.
        
        Args:
            db: Database session
            field: Field name
            values: List of field values
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List[ModelType]: List of records
        """
        if not hasattr(self.model, field):
            return []
        
        return (
            db.query(self.model)
            .filter(getattr(self.model, field).in_(values))
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def bulk_create(self, db: Session, *, objs_in: List[Dict[str, Any]], created_by: Optional[int] = None) -> List[ModelType]:
        """
        Create multiple records in bulk.
        
        Args:
            db: Database session
            objs_in: List of object data
            created_by: User ID who created the records
            
        Returns:
            List[ModelType]: List of created records
        """
        db_objs = []
        for obj_in in objs_in:
            if created_by is not None:
                obj_in["created_by"] = created_by
                obj_in["updated_by"] = created_by
            db_objs.append(self.model(**obj_in))
        
        db.add_all(db_objs)
        db.commit()
        
        for db_obj in db_objs:
            db.refresh(db_obj)
        
        return db_objs
    
    def bulk_update(
        self,
        db: Session,
        *,
        updates: List[Dict[str, Any]],
        updated_by: Optional[int] = None
    ) -> List[ModelType]:
        """
        Update multiple records in bulk.
        
        Args:
            db: Database session
            updates: List of update data (must include 'id' field)
            updated_by: User ID who updated the records
            
        Returns:
            List[ModelType]: List of updated records
        """
        updated_objs = []
        
        for update_data in updates:
            if "id" not in update_data:
                continue
            
            obj_id = update_data.pop("id")
            db_obj = self.get(db, obj_id)
            
            if db_obj:
                if updated_by is not None:
                    update_data["updated_by"] = updated_by
                
                for field, value in update_data.items():
                    if hasattr(db_obj, field):
                        setattr(db_obj, field, value)
                
                updated_objs.append(db_obj)
        
        db.commit()
        
        for db_obj in updated_objs:
            db.refresh(db_obj)
        
        return updated_objs
