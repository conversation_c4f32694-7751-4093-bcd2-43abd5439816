"""
Base schemas for common response patterns.
"""
from datetime import datetime
from typing import Any, Dict, Generic, List, Optional, TypeVar

from pydantic import BaseModel, Field

DataType = TypeVar("DataType")


class BaseSchema(BaseModel):
    """Base schema with common fields."""
    
    class Config:
        from_attributes = True
        arbitrary_types_allowed = True


class TimestampSchema(BaseSchema):
    """Schema with timestamp fields."""
    
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class AuditSchema(TimestampSchema):
    """Schema with audit fields."""
    
    created_by: Optional[int] = Field(None, description="创建人ID")
    updated_by: Optional[int] = Field(None, description="更新人ID")


class IDSchema(BaseSchema):
    """Schema with ID field."""
    
    id: int = Field(..., description="记录ID")


class PaginationParams(BaseSchema):
    """Pagination parameters."""
    
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")
    
    @property
    def skip(self) -> int:
        """Calculate skip value for database query."""
        return (self.page - 1) * self.page_size
    
    @property
    def limit(self) -> int:
        """Get limit value for database query."""
        return self.page_size


class PaginationMeta(BaseSchema):
    """Pagination metadata."""
    
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total: int = Field(..., description="总记录数")
    total_pages: int = Field(..., description="总页数")
    has_next: bool = Field(..., description="是否有下一页")
    has_prev: bool = Field(..., description="是否有上一页")


class PaginatedResponse(BaseSchema, Generic[DataType]):
    """Paginated response wrapper."""
    
    data: List[DataType] = Field(..., description="数据列表")
    meta: PaginationMeta = Field(..., description="分页信息")
    
    @classmethod
    def create(
        cls,
        data: List[DataType],
        page: int,
        page_size: int,
        total: int
    ) -> "PaginatedResponse[DataType]":
        """
        Create paginated response.
        
        Args:
            data: Data list
            page: Current page
            page_size: Page size
            total: Total count
            
        Returns:
            PaginatedResponse: Paginated response
        """
        total_pages = (total + page_size - 1) // page_size
        
        meta = PaginationMeta(
            page=page,
            page_size=page_size,
            total=total,
            total_pages=total_pages,
            has_next=page < total_pages,
            has_prev=page > 1
        )
        
        return cls(data=data, meta=meta)


class ResponseModel(BaseSchema, Generic[DataType]):
    """Standard API response model."""
    
    success: bool = Field(True, description="是否成功")
    message: str = Field("", description="响应消息")
    data: Optional[DataType] = Field(None, description="响应数据")
    errors: Optional[List[str]] = Field(None, description="错误信息")
    
    @classmethod
    def success_response(
        cls,
        data: Optional[DataType] = None,
        message: str = "操作成功"
    ) -> "ResponseModel[DataType]":
        """
        Create success response.
        
        Args:
            data: Response data
            message: Success message
            
        Returns:
            ResponseModel: Success response
        """
        return cls(success=True, message=message, data=data)
    
    @classmethod
    def error_response(
        cls,
        message: str = "操作失败",
        errors: Optional[List[str]] = None
    ) -> "ResponseModel[None]":
        """
        Create error response.
        
        Args:
            message: Error message
            errors: List of error details
            
        Returns:
            ResponseModel: Error response
        """
        return cls(success=False, message=message, errors=errors)


class TokenResponse(BaseSchema):
    """Token response schema."""
    
    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间(秒)")


class LoginRequest(BaseSchema):
    """Login request schema."""
    
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, max_length=100, description="密码")


class RefreshTokenRequest(BaseSchema):
    """Refresh token request schema."""
    
    refresh_token: str = Field(..., description="刷新令牌")


class ChangePasswordRequest(BaseSchema):
    """Change password request schema."""
    
    old_password: str = Field(..., min_length=6, max_length=100, description="旧密码")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")
    confirm_password: str = Field(..., min_length=6, max_length=100, description="确认密码")
    
    def validate_passwords_match(self) -> bool:
        """Validate that new password and confirm password match."""
        return self.new_password == self.confirm_password


class SearchParams(BaseSchema):
    """Search parameters."""
    
    query: str = Field("", description="搜索关键词")
    filters: Optional[Dict[str, Any]] = Field(None, description="过滤条件")
    sort_by: Optional[str] = Field(None, description="排序字段")
    sort_desc: bool = Field(False, description="是否降序排列")


class BulkOperationRequest(BaseSchema):
    """Bulk operation request."""
    
    ids: List[int] = Field(..., min_items=1, description="操作的ID列表")
    operation: str = Field(..., description="操作类型")
    params: Optional[Dict[str, Any]] = Field(None, description="操作参数")
