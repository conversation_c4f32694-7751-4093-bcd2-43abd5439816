"""
User-related schemas.
"""
from typing import Optional

from pydantic import EmailStr, Field, validator

from app.models.user import UserStatus
from app.schemas.base import AuditSchema, BaseSchema, IDSchema, LoginRequest, ChangePasswordRequest


# Admin schemas
class AdminBase(BaseSchema):
    """Base admin schema."""
    
    username: str = Field(..., min_length=3, max_length=50, description="管理员用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否超级管理员")


class AdminCreate(AdminBase):
    """Admin creation schema."""
    
    password: str = Field(..., min_length=6, max_length=100, description="密码")


class AdminUpdate(BaseSchema):
    """Admin update schema."""
    
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="管理员用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_active: Optional[bool] = Field(None, description="是否激活")
    is_superuser: Optional[bool] = Field(None, description="是否超级管理员")
    status: Optional[UserStatus] = Field(None, description="用户状态")


class AdminResponse(AdminBase, IDSchema, AuditSchema):
    """Admin response schema."""
    
    status: UserStatus = Field(..., description="用户状态")


# Tenant schemas
class TenantBase(BaseSchema):
    """Base tenant schema."""
    
    name: str = Field(..., min_length=2, max_length=100, description="租户名称")
    code: str = Field(..., min_length=2, max_length=50, description="租户代码")
    email: EmailStr = Field(..., description="租户邮箱")
    description: Optional[str] = Field(None, description="租户描述")
    is_active: bool = Field(True, description="是否激活")
    
    @validator('code')
    def validate_code(cls, v):
        """Validate tenant code format."""
        if not v.isalnum():
            raise ValueError('租户代码只能包含字母和数字')
        return v.lower()


class TenantCreate(TenantBase):
    """Tenant creation schema."""
    
    password: str = Field(..., min_length=6, max_length=100, description="密码")


class TenantUpdate(BaseSchema):
    """Tenant update schema."""
    
    name: Optional[str] = Field(None, min_length=2, max_length=100, description="租户名称")
    email: Optional[EmailStr] = Field(None, description="租户邮箱")
    description: Optional[str] = Field(None, description="租户描述")
    is_active: Optional[bool] = Field(None, description="是否激活")
    status: Optional[UserStatus] = Field(None, description="租户状态")


class TenantResponse(TenantBase, IDSchema, AuditSchema):
    """Tenant response schema."""
    
    status: UserStatus = Field(..., description="租户状态")


# User schemas
class UserBase(BaseSchema):
    """Base user schema."""
    
    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_active: bool = Field(True, description="是否激活")


class UserCreate(UserBase):
    """User creation schema."""
    
    password: str = Field(..., min_length=6, max_length=100, description="密码")
    tenant_id: int = Field(..., description="所属租户ID")


class UserUpdate(BaseSchema):
    """User update schema."""
    
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    is_active: Optional[bool] = Field(None, description="是否激活")
    status: Optional[UserStatus] = Field(None, description="用户状态")


class UserResponse(UserBase, IDSchema, AuditSchema):
    """User response schema."""
    
    tenant_id: int = Field(..., description="所属租户ID")
    status: UserStatus = Field(..., description="用户状态")


class UserWithTenant(UserResponse):
    """User response with tenant information."""
    
    tenant: Optional[TenantResponse] = Field(None, description="租户信息")


# Login schemas
class AdminLoginRequest(BaseSchema):
    """Admin login request."""
    
    username: str = Field(..., description="管理员用户名")
    password: str = Field(..., description="密码")


class TenantLoginRequest(BaseSchema):
    """Tenant login request."""
    
    code: str = Field(..., description="租户代码")
    password: str = Field(..., description="密码")


class UserLoginRequest(BaseSchema):
    """User login request."""
    
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")
    tenant_code: str = Field(..., description="租户代码")


# Profile schemas
class ProfileUpdate(BaseSchema):
    """Profile update schema."""
    
    full_name: Optional[str] = Field(None, max_length=100, description="全名")
    email: Optional[EmailStr] = Field(None, description="邮箱地址")


class PasswordChange(BaseSchema):
    """Password change schema."""
    
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, max_length=100, description="新密码")
    confirm_password: str = Field(..., description="确认新密码")
    
    @validator('confirm_password')
    def passwords_match(cls, v, values):
        """Validate that passwords match."""
        if 'new_password' in values and v != values['new_password']:
            raise ValueError('新密码和确认密码不匹配')
        return v


# Statistics schemas
class UserStats(BaseSchema):
    """User statistics schema."""
    
    total_users: int = Field(..., description="总用户数")
    active_users: int = Field(..., description="活跃用户数")
    inactive_users: int = Field(..., description="非活跃用户数")
    users_by_tenant: dict = Field(..., description="按租户分组的用户数")


class TenantStats(BaseSchema):
    """Tenant statistics schema."""

    total_tenants: int = Field(..., description="总租户数")
    active_tenants: int = Field(..., description="活跃租户数")
    inactive_tenants: int = Field(..., description="非活跃租户数")
    tenant_user_counts: dict = Field(..., description="租户用户数统计")


# Login schemas
class AdminLoginRequest(LoginRequest):
    """Admin login request schema."""
    pass


class TenantLoginRequest(BaseSchema):
    """Tenant login request schema."""

    code: str = Field(..., min_length=2, max_length=20, description="租户代码")
    password: str = Field(..., min_length=6, max_length=100, description="密码")


class UserLoginRequest(BaseSchema):
    """User login request schema."""

    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=6, max_length=100, description="密码")
    tenant_code: str = Field(..., min_length=2, max_length=20, description="租户代码")
