"""
Base event system implementation.
"""
import asyncio
import json
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Type

from sqlalchemy.orm import Session

from app.dao.event import event_dao, event_log_dao, event_subscription_dao
from app.models.event import Event, EventPriority, EventStatus

logger = logging.getLogger(__name__)


class BaseEventHandler(ABC):
    """Base class for event handlers."""
    
    def __init__(self, name: str, priority: int = 0):
        """
        Initialize event handler.
        
        Args:
            name: Handler name
            priority: Handler priority (higher values execute first)
        """
        self.name = name
        self.priority = priority
    
    @abstractmethod
    async def handle(self, event_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle the event.
        
        Args:
            event_data: Event data
            context: Additional context
            
        Returns:
            Dict[str, Any]: Handler result
        """
        pass
    
    def can_handle(self, event_type: str) -> bool:
        """
        Check if this handler can handle the event type.
        
        Args:
            event_type: Event type
            
        Returns:
            bool: True if can handle
        """
        return True
    
    async def on_success(self, event_data: Dict[str, Any], result: Dict[str, Any]) -> None:
        """
        Called when handler succeeds.
        
        Args:
            event_data: Event data
            result: Handler result
        """
        pass
    
    async def on_error(self, event_data: Dict[str, Any], error: Exception) -> None:
        """
        Called when handler fails.
        
        Args:
            event_data: Event data
            error: Exception that occurred
        """
        logger.error(f"Handler {self.name} failed: {error}")


class EventBus:
    """Event bus for publishing and subscribing to events."""
    
    def __init__(self):
        self._handlers: Dict[str, List[BaseEventHandler]] = {}
        self._global_handlers: List[BaseEventHandler] = []
    
    def subscribe(self, event_type: str, handler: BaseEventHandler) -> None:
        """
        Subscribe a handler to an event type.
        
        Args:
            event_type: Event type
            handler: Event handler
        """
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        
        self._handlers[event_type].append(handler)
        # Sort by priority (higher priority first)
        self._handlers[event_type].sort(key=lambda h: h.priority, reverse=True)
        
        logger.info(f"Subscribed handler {handler.name} to event type {event_type}")
    
    def subscribe_global(self, handler: BaseEventHandler) -> None:
        """
        Subscribe a handler to all events.
        
        Args:
            handler: Event handler
        """
        self._global_handlers.append(handler)
        self._global_handlers.sort(key=lambda h: h.priority, reverse=True)
        
        logger.info(f"Subscribed global handler {handler.name}")
    
    def unsubscribe(self, event_type: str, handler: BaseEventHandler) -> None:
        """
        Unsubscribe a handler from an event type.
        
        Args:
            event_type: Event type
            handler: Event handler
        """
        if event_type in self._handlers:
            self._handlers[event_type] = [h for h in self._handlers[event_type] if h.name != handler.name]
        
        logger.info(f"Unsubscribed handler {handler.name} from event type {event_type}")
    
    def get_handlers(self, event_type: str) -> List[BaseEventHandler]:
        """
        Get handlers for an event type.
        
        Args:
            event_type: Event type
            
        Returns:
            List[BaseEventHandler]: List of handlers
        """
        handlers = []
        
        # Add specific handlers
        if event_type in self._handlers:
            handlers.extend(self._handlers[event_type])
        
        # Add global handlers that can handle this event type
        for handler in self._global_handlers:
            if handler.can_handle(event_type):
                handlers.append(handler)
        
        # Sort by priority
        handlers.sort(key=lambda h: h.priority, reverse=True)
        
        return handlers
    
    async def publish(
        self,
        event_type: str,
        data: Dict[str, Any],
        priority: EventPriority = EventPriority.NORMAL,
        context: Optional[Dict[str, Any]] = None,
        created_by: Optional[int] = None
    ) -> Event:
        """
        Publish an event.
        
        Args:
            event_type: Event type
            data: Event data
            priority: Event priority
            context: Additional context
            created_by: User ID who created the event
            
        Returns:
            Event: Created event
        """
        # Create event in database
        from app.db.database import SessionLocal
        
        db = SessionLocal()
        try:
            event_data = {
                "name": event_type,
                "event_type": event_type,
                "source": "app",
                "payload": data,
                "priority": priority,
                "status": EventStatus.PENDING,
                "retry_count": 0,
                "max_retries": 3,
            }
            
            event = event_dao.create(db, obj_in=event_data, created_by=created_by)
            
            # Process event asynchronously
            asyncio.create_task(self._process_event(event.id, context))
            
            return event
        finally:
            db.close()
    
    async def _process_event(self, event_id: int, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Process an event by calling all registered handlers.
        
        Args:
            event_id: Event ID
            context: Additional context
        """
        from app.db.database import SessionLocal
        
        db = SessionLocal()
        try:
            # Get event from database
            event = event_dao.get(db, event_id)
            if not event:
                logger.error(f"Event {event_id} not found")
                return
            
            # Update event status to processing
            event_dao.update_status(db, event_id=event_id, status=EventStatus.PROCESSING)
            
            # Get handlers for this event type
            handlers = self.get_handlers(event.event_type)
            
            if not handlers:
                logger.warning(f"No handlers found for event type {event.event_type}")
                event_dao.update_status(db, event_id=event_id, status=EventStatus.COMPLETED)
                return
            
            # Process each handler
            all_success = True
            for handler in handlers:
                try:
                    start_time = datetime.utcnow()
                    
                    # Execute handler
                    result = await handler.handle(event.payload, context)
                    
                    # Calculate execution time
                    execution_time = (datetime.utcnow() - start_time).total_seconds()
                    
                    # Log success
                    event_log_dao.create_log(
                        db,
                        event_id=event_id,
                        handler_name=handler.name,
                        status=EventStatus.COMPLETED,
                        execution_time=execution_time,
                        result=result,
                        created_by=event.created_by
                    )
                    
                    # Call success callback
                    await handler.on_success(event.payload, result)
                    
                    logger.info(f"Handler {handler.name} completed for event {event_id}")
                    
                except Exception as e:
                    all_success = False
                    
                    # Calculate execution time
                    execution_time = (datetime.utcnow() - start_time).total_seconds()
                    
                    # Log error
                    event_log_dao.create_log(
                        db,
                        event_id=event_id,
                        handler_name=handler.name,
                        status=EventStatus.FAILED,
                        execution_time=execution_time,
                        error_message=str(e),
                        created_by=event.created_by
                    )
                    
                    # Call error callback
                    await handler.on_error(event.payload, e)
                    
                    logger.error(f"Handler {handler.name} failed for event {event_id}: {e}")
            
            # Update final event status
            if all_success:
                event_dao.update_status(db, event_id=event_id, status=EventStatus.COMPLETED)
            else:
                event_dao.update_status(
                    db,
                    event_id=event_id,
                    status=EventStatus.FAILED,
                    error_message="One or more handlers failed",
                    increment_retry=True
                )
        
        except Exception as e:
            logger.error(f"Error processing event {event_id}: {e}")
            event_dao.update_status(
                db,
                event_id=event_id,
                status=EventStatus.FAILED,
                error_message=str(e),
                increment_retry=True
            )
        finally:
            db.close()
    
    async def retry_failed_events(self, limit: int = 10) -> None:
        """
        Retry failed events that haven't exceeded max retries.
        
        Args:
            limit: Maximum number of events to retry
        """
        from app.db.database import SessionLocal
        
        db = SessionLocal()
        try:
            failed_events = event_dao.get_failed_events(db, limit=limit)
            
            for event in failed_events:
                logger.info(f"Retrying event {event.id} (attempt {event.retry_count + 1})")
                asyncio.create_task(self._process_event(event.id))
        finally:
            db.close()


# Global event bus instance
event_bus = EventBus()
