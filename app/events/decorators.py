"""
Event system decorators.
"""
import asyncio
import functools
import logging
from typing import Any, Callable, Dict, List, Optional

from app.events.base import BaseEventHandler, event_bus

logger = logging.getLogger(__name__)


def event_handler(
    event_types: List[str],
    priority: int = 0,
    name: Optional[str] = None
):
    """
    Decorator to register a function as an event handler.
    
    Args:
        event_types: List of event types to handle
        priority: Handler priority
        name: Handler name (defaults to function name)
    """
    def decorator(func: Callable) -> Callable:
        handler_name = name or func.__name__
        
        class DecoratedHandler(BaseEventHandler):
            def __init__(self):
                super().__init__(handler_name, priority)
                self.func = func
            
            async def handle(self, event_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
                if asyncio.iscoroutinefunction(self.func):
                    return await self.func(event_data, context)
                else:
                    return self.func(event_data, context)
            
            def can_handle(self, event_type: str) -> bool:
                return event_type in event_types
        
        # Register handler
        handler = DecoratedHandler()
        for event_type in event_types:
            event_bus.subscribe(event_type, handler)
        
        logger.info(f"Registered event handler {handler_name} for types: {event_types}")
        
        return func
    
    return decorator


def global_event_handler(priority: int = 0, name: Optional[str] = None):
    """
    Decorator to register a function as a global event handler.
    
    Args:
        priority: Handler priority
        name: Handler name (defaults to function name)
    """
    def decorator(func: Callable) -> Callable:
        handler_name = name or func.__name__
        
        class GlobalDecoratedHandler(BaseEventHandler):
            def __init__(self):
                super().__init__(handler_name, priority)
                self.func = func
            
            async def handle(self, event_data: Dict[str, Any], context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
                if asyncio.iscoroutinefunction(self.func):
                    return await self.func(event_data, context)
                else:
                    return self.func(event_data, context)
        
        # Register global handler
        handler = GlobalDecoratedHandler()
        event_bus.subscribe_global(handler)
        
        logger.info(f"Registered global event handler {handler_name}")
        
        return func
    
    return decorator


def publish_event(
    event_type: str,
    priority: str = "normal",
    extract_data: Optional[Callable] = None
):
    """
    Decorator to automatically publish an event when a function is called.
    
    Args:
        event_type: Event type to publish
        priority: Event priority
        extract_data: Function to extract event data from function args/kwargs
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Execute original function
            result = await func(*args, **kwargs)
            
            # Extract event data
            if extract_data:
                event_data = extract_data(*args, **kwargs, result=result)
            else:
                event_data = {
                    "function": func.__name__,
                    "args": args,
                    "kwargs": kwargs,
                    "result": result
                }
            
            # Publish event
            from app.models.event import EventPriority
            priority_map = {
                "low": EventPriority.LOW,
                "normal": EventPriority.NORMAL,
                "high": EventPriority.HIGH,
                "critical": EventPriority.CRITICAL
            }
            
            await event_bus.publish(
                event_type=event_type,
                data=event_data,
                priority=priority_map.get(priority, EventPriority.NORMAL)
            )
            
            return result
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Execute original function
            result = func(*args, **kwargs)
            
            # Extract event data
            if extract_data:
                event_data = extract_data(*args, **kwargs, result=result)
            else:
                event_data = {
                    "function": func.__name__,
                    "args": args,
                    "kwargs": kwargs,
                    "result": result
                }
            
            # Publish event asynchronously
            import asyncio
            from app.models.event import EventPriority
            
            priority_map = {
                "low": EventPriority.LOW,
                "normal": EventPriority.NORMAL,
                "high": EventPriority.HIGH,
                "critical": EventPriority.CRITICAL
            }
            
            asyncio.create_task(
                event_bus.publish(
                    event_type=event_type,
                    data=event_data,
                    priority=priority_map.get(priority, EventPriority.NORMAL)
                )
            )
            
            return result
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def conditional_event(
    event_type: str,
    condition: Callable,
    priority: str = "normal",
    extract_data: Optional[Callable] = None
):
    """
    Decorator to conditionally publish an event based on function result.
    
    Args:
        event_type: Event type to publish
        condition: Function to check if event should be published
        priority: Event priority
        extract_data: Function to extract event data from function args/kwargs
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Execute original function
            result = await func(*args, **kwargs)
            
            # Check condition
            if condition(*args, **kwargs, result=result):
                # Extract event data
                if extract_data:
                    event_data = extract_data(*args, **kwargs, result=result)
                else:
                    event_data = {
                        "function": func.__name__,
                        "args": args,
                        "kwargs": kwargs,
                        "result": result
                    }
                
                # Publish event
                from app.models.event import EventPriority
                priority_map = {
                    "low": EventPriority.LOW,
                    "normal": EventPriority.NORMAL,
                    "high": EventPriority.HIGH,
                    "critical": EventPriority.CRITICAL
                }
                
                await event_bus.publish(
                    event_type=event_type,
                    data=event_data,
                    priority=priority_map.get(priority, EventPriority.NORMAL)
                )
            
            return result
        
        @functools.wraps(func)
        def sync_wrapper(*args, **kwargs):
            # Execute original function
            result = func(*args, **kwargs)
            
            # Check condition
            if condition(*args, **kwargs, result=result):
                # Extract event data
                if extract_data:
                    event_data = extract_data(*args, **kwargs, result=result)
                else:
                    event_data = {
                        "function": func.__name__,
                        "args": args,
                        "kwargs": kwargs,
                        "result": result
                    }
                
                # Publish event asynchronously
                import asyncio
                from app.models.event import EventPriority
                
                priority_map = {
                    "low": EventPriority.LOW,
                    "normal": EventPriority.NORMAL,
                    "high": EventPriority.HIGH,
                    "critical": EventPriority.CRITICAL
                }
                
                asyncio.create_task(
                    event_bus.publish(
                        event_type=event_type,
                        data=event_data,
                        priority=priority_map.get(priority, EventPriority.NORMAL)
                    )
                )
            
            return result
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator
