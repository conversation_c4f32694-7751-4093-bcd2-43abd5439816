"""
Initialize default permissions and roles.
"""
from sqlalchemy.orm import Session

from app.models.permission import Permission, PermissionType, ResourceType, Role, RolePermission


def create_default_permissions(db: Session) -> None:
    """Create default permissions for the system."""
    
    # Define default permissions
    default_permissions = [
        # Admin permissions
        ("admin:create", "Create Admin", ResourceType.ADMIN, PermissionType.CREATE),
        ("admin:read", "Read Admin", ResourceType.ADMIN, PermissionType.READ),
        ("admin:update", "Update Admin", ResourceType.ADMIN, PermissionType.UPDATE),
        ("admin:delete", "Delete Admin", ResourceType.ADMIN, PermissionType.DELETE),
        ("admin:manage", "Manage Admin", ResourceType.ADMIN, PermissionType.MANAGE),
        
        # Tenant permissions
        ("tenant:create", "Create Tenant", ResourceType.TENANT, PermissionType.CREATE),
        ("tenant:read", "Read Tenant", ResourceType.TENANT, PermissionType.READ),
        ("tenant:update", "Update Tenant", ResourceType.TENANT, PermissionType.UPDATE),
        ("tenant:delete", "Delete Tenant", ResourceType.TENANT, PermissionType.DELETE),
        ("tenant:manage", "Manage Tenant", ResourceType.TENANT, PermissionType.MANAGE),
        
        # User permissions
        ("user:create", "Create User", ResourceType.USER, PermissionType.CREATE),
        ("user:read", "Read User", ResourceType.USER, PermissionType.READ),
        ("user:update", "Update User", ResourceType.USER, PermissionType.UPDATE),
        ("user:delete", "Delete User", ResourceType.USER, PermissionType.DELETE),
        ("user:manage", "Manage User", ResourceType.USER, PermissionType.MANAGE),
        
        # Permission permissions
        ("permission:create", "Create Permission", ResourceType.PERMISSION, PermissionType.CREATE),
        ("permission:read", "Read Permission", ResourceType.PERMISSION, PermissionType.READ),
        ("permission:update", "Update Permission", ResourceType.PERMISSION, PermissionType.UPDATE),
        ("permission:delete", "Delete Permission", ResourceType.PERMISSION, PermissionType.DELETE),
        ("permission:manage", "Manage Permission", ResourceType.PERMISSION, PermissionType.MANAGE),
        
        # Role permissions
        ("role:create", "Create Role", ResourceType.ROLE, PermissionType.CREATE),
        ("role:read", "Read Role", ResourceType.ROLE, PermissionType.READ),
        ("role:update", "Update Role", ResourceType.ROLE, PermissionType.UPDATE),
        ("role:delete", "Delete Role", ResourceType.ROLE, PermissionType.DELETE),
        ("role:manage", "Manage Role", ResourceType.ROLE, PermissionType.MANAGE),
        
        # System permissions
        ("system:read", "Read System", ResourceType.SYSTEM, PermissionType.READ),
        ("system:manage", "Manage System", ResourceType.SYSTEM, PermissionType.MANAGE),
        ("system:execute", "Execute System", ResourceType.SYSTEM, PermissionType.EXECUTE),
    ]
    
    for code, name, resource_type, permission_type in default_permissions:
        # Check if permission already exists
        existing = db.query(Permission).filter(Permission.code == code).first()
        if not existing:
            permission = Permission(
                name=name,
                code=code,
                resource_type=resource_type,
                permission_type=permission_type,
                is_active=True
            )
            db.add(permission)
    
    db.commit()


def create_default_roles(db: Session) -> None:
    """Create default roles for the system."""
    
    # Define default roles
    default_roles = [
        ("super_admin", "Super Administrator", "Full system access"),
        ("admin", "Administrator", "Administrative access"),
        ("tenant_admin", "Tenant Administrator", "Tenant management access"),
        ("tenant_user", "Tenant User", "Basic tenant user access"),
        ("user", "User", "Basic user access"),
    ]
    
    for code, name, description in default_roles:
        # Check if role already exists
        existing = db.query(Role).filter(Role.code == code).first()
        if not existing:
            role = Role(
                name=name,
                code=code,
                description=description,
                is_active=True
            )
            db.add(role)
    
    db.commit()


def assign_role_permissions(db: Session) -> None:
    """Assign permissions to default roles."""
    
    # Define role-permission mappings
    role_permissions = {
        "super_admin": [
            # All permissions
            "admin:create", "admin:read", "admin:update", "admin:delete", "admin:manage",
            "tenant:create", "tenant:read", "tenant:update", "tenant:delete", "tenant:manage",
            "user:create", "user:read", "user:update", "user:delete", "user:manage",
            "permission:create", "permission:read", "permission:update", "permission:delete", "permission:manage",
            "role:create", "role:read", "role:update", "role:delete", "role:manage",
            "system:read", "system:manage", "system:execute",
        ],
        "admin": [
            # Admin permissions (excluding super admin functions)
            "tenant:create", "tenant:read", "tenant:update", "tenant:delete", "tenant:manage",
            "user:create", "user:read", "user:update", "user:delete", "user:manage",
            "permission:read", "role:read",
            "system:read",
        ],
        "tenant_admin": [
            # Tenant management permissions
            "user:create", "user:read", "user:update", "user:delete", "user:manage",
            "permission:read", "role:read",
        ],
        "tenant_user": [
            # Basic tenant user permissions
            "user:read", "user:update",
        ],
        "user": [
            # Basic user permissions
            "user:read",
        ],
    }
    
    for role_code, permission_codes in role_permissions.items():
        role = db.query(Role).filter(Role.code == role_code).first()
        if not role:
            continue
        
        for permission_code in permission_codes:
            permission = db.query(Permission).filter(Permission.code == permission_code).first()
            if not permission:
                continue
            
            # Check if role-permission already exists
            existing = db.query(RolePermission).filter(
                RolePermission.role_id == role.id,
                RolePermission.permission_id == permission.id
            ).first()
            
            if not existing:
                role_permission = RolePermission(
                    role_id=role.id,
                    permission_id=permission.id
                )
                db.add(role_permission)
    
    db.commit()


def initialize_permissions_and_roles(db: Session) -> None:
    """Initialize all default permissions and roles."""
    create_default_permissions(db)
    create_default_roles(db)
    assign_role_permissions(db)
