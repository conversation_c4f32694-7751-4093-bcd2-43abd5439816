"""
Security utilities for authentication and authorization.
"""
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Union

import arrow
from jose import JWTError, jwt
from passlib.context import CryptContext

from app.core.config import settings

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None,
    user_type: str = "user",
    tenant_id: Optional[int] = None,
    additional_claims: Optional[Dict[str, Any]] = None
) -> str:
    """
    Create JWT access token.
    
    Args:
        subject: Token subject (usually user ID)
        expires_delta: Token expiration time
        user_type: Type of user (admin, tenant, user)
        tenant_id: Tenant ID for multi-tenant support
        additional_claims: Additional claims to include
        
    Returns:
        str: JWT token
    """
    if expires_delta:
        expire = arrow.utcnow() + expires_delta
    else:
        expire = arrow.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode = {
        "exp": expire.timestamp(),
        "iat": arrow.utcnow().timestamp(),
        "sub": str(subject),
        "type": "access",
        "user_type": user_type,
    }
    
    if tenant_id is not None:
        to_encode["tenant_id"] = tenant_id
    
    if additional_claims:
        to_encode.update(additional_claims)
    
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def create_refresh_token(
    subject: Union[str, Any],
    expires_delta: Optional[timedelta] = None,
    user_type: str = "user",
    tenant_id: Optional[int] = None
) -> str:
    """
    Create JWT refresh token.
    
    Args:
        subject: Token subject (usually user ID)
        expires_delta: Token expiration time
        user_type: Type of user (admin, tenant, user)
        tenant_id: Tenant ID for multi-tenant support
        
    Returns:
        str: JWT refresh token
    """
    if expires_delta:
        expire = arrow.utcnow() + expires_delta
    else:
        expire = arrow.utcnow() + timedelta(days=settings.refresh_token_expire_days)
    
    to_encode = {
        "exp": expire.timestamp(),
        "iat": arrow.utcnow().timestamp(),
        "sub": str(subject),
        "type": "refresh",
        "user_type": user_type,
    }
    
    if tenant_id is not None:
        to_encode["tenant_id"] = tenant_id
    
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt


def verify_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Verify and decode JWT token.
    
    Args:
        token: JWT token to verify
        
    Returns:
        Optional[Dict[str, Any]]: Token payload if valid, None otherwise
    """
    try:
        payload = jwt.decode(
            token,
            settings.secret_key,
            algorithms=[settings.algorithm]
        )
        return payload
    except JWTError:
        return None


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
        
    Returns:
        bool: True if password is correct
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    Hash a password.
    
    Args:
        password: Plain text password
        
    Returns:
        str: Hashed password
    """
    return pwd_context.hash(password)


def is_token_expired(payload: Dict[str, Any]) -> bool:
    """
    Check if token is expired.
    
    Args:
        payload: Token payload
        
    Returns:
        bool: True if token is expired
    """
    exp = payload.get("exp")
    if not exp:
        return True
    
    return arrow.utcnow().timestamp() > exp


def get_token_subject(payload: Dict[str, Any]) -> Optional[str]:
    """
    Get token subject.
    
    Args:
        payload: Token payload
        
    Returns:
        Optional[str]: Token subject
    """
    return payload.get("sub")


def get_token_type(payload: Dict[str, Any]) -> Optional[str]:
    """
    Get token type.
    
    Args:
        payload: Token payload
        
    Returns:
        Optional[str]: Token type (access, refresh)
    """
    return payload.get("type")


def get_user_type(payload: Dict[str, Any]) -> Optional[str]:
    """
    Get user type from token.
    
    Args:
        payload: Token payload
        
    Returns:
        Optional[str]: User type (admin, tenant, user)
    """
    return payload.get("user_type")


def get_tenant_id(payload: Dict[str, Any]) -> Optional[int]:
    """
    Get tenant ID from token.
    
    Args:
        payload: Token payload
        
    Returns:
        Optional[int]: Tenant ID
    """
    tenant_id = payload.get("tenant_id")
    return int(tenant_id) if tenant_id is not None else None


def create_token_pair(
    user_id: int,
    user_type: str = "user",
    tenant_id: Optional[int] = None,
    additional_claims: Optional[Dict[str, Any]] = None
) -> Dict[str, str]:
    """
    Create access and refresh token pair.
    
    Args:
        user_id: User ID
        user_type: Type of user (admin, tenant, user)
        tenant_id: Tenant ID for multi-tenant support
        additional_claims: Additional claims for access token
        
    Returns:
        Dict[str, str]: Access and refresh tokens
    """
    access_token = create_access_token(
        subject=user_id,
        user_type=user_type,
        tenant_id=tenant_id,
        additional_claims=additional_claims
    )
    refresh_token = create_refresh_token(
        subject=user_id,
        user_type=user_type,
        tenant_id=tenant_id
    )
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }
