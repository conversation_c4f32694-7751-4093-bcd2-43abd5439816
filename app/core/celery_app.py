"""
Celery application configuration.
"""
import logging
from typing import Any, Dict

from celery import Celery
from celery.signals import after_setup_logger, task_failure, task_success

from app.core.config import settings

logger = logging.getLogger(__name__)

# Create Celery app
celery_app = Celery(
    "pxblangs",
    broker=settings.celery_broker_url,
    backend=settings.celery_result_backend,
    include=[
        "app.tasks.user_tasks",
        "app.tasks.email_tasks",
        "app.tasks.cleanup_tasks",
        "app.tasks.report_tasks",
    ]
)

# Celery configuration
celery_app.conf.update(
    # Task settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    
    # Task routing
    task_routes={
        "app.tasks.email_tasks.*": {"queue": "email"},
        "app.tasks.cleanup_tasks.*": {"queue": "cleanup"},
        "app.tasks.report_tasks.*": {"queue": "reports"},
        "app.tasks.user_tasks.*": {"queue": "users"},
    },
    
    # Task execution settings
    task_always_eager=False,
    task_eager_propagates=True,
    task_ignore_result=False,
    task_store_eager_result=True,
    
    # Worker settings
    worker_prefetch_multiplier=1,
    worker_max_tasks_per_child=1000,
    worker_disable_rate_limits=False,
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_persistent=True,
    
    # Retry settings
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    
    # Beat schedule (for periodic tasks)
    beat_schedule={
        "cleanup-expired-tokens": {
            "task": "app.tasks.cleanup_tasks.cleanup_expired_tokens",
            "schedule": 3600.0,  # Every hour
        },
        "cleanup-old-logs": {
            "task": "app.tasks.cleanup_tasks.cleanup_old_logs",
            "schedule": 86400.0,  # Every day
        },
        "generate-daily-reports": {
            "task": "app.tasks.report_tasks.generate_daily_reports",
            "schedule": 86400.0,  # Every day at midnight
            "options": {"queue": "reports"}
        },
    },
    beat_scheduler="celery.beat:PersistentScheduler",
)


@after_setup_logger.connect
def setup_loggers(logger, *args, **kwargs):
    """Setup Celery loggers."""
    # Import here to avoid circular imports
    from app.core.logging import setup_logging
    
    # Setup application logging
    setup_logging()
    
    # Configure Celery logger
    celery_logger = logging.getLogger("celery")
    celery_logger.setLevel(logging.INFO)


@task_success.connect
def task_success_handler(sender=None, task_id=None, result=None, retries=None, einfo=None, **kwargs):
    """Handle successful task completion."""
    logger.info(f"Task {sender.name} ({task_id}) completed successfully")


@task_failure.connect
def task_failure_handler(sender=None, task_id=None, exception=None, traceback=None, einfo=None, **kwargs):
    """Handle task failure."""
    logger.error(f"Task {sender.name} ({task_id}) failed: {exception}")


# Task base class with common functionality
class BaseTask(celery_app.Task):
    """Base task class with common functionality."""
    
    def on_success(self, retval: Any, task_id: str, args: tuple, kwargs: dict) -> None:
        """Called on task success."""
        logger.info(f"Task {self.name} ({task_id}) succeeded with result: {retval}")
    
    def on_failure(self, exc: Exception, task_id: str, args: tuple, kwargs: dict, einfo) -> None:
        """Called on task failure."""
        logger.error(f"Task {self.name} ({task_id}) failed: {exc}")
    
    def on_retry(self, exc: Exception, task_id: str, args: tuple, kwargs: dict, einfo) -> None:
        """Called on task retry."""
        logger.warning(f"Task {self.name} ({task_id}) retrying due to: {exc}")


# Set default task base class
celery_app.Task = BaseTask


def get_task_info(task_id: str) -> Dict[str, Any]:
    """
    Get task information.
    
    Args:
        task_id: Task ID
        
    Returns:
        Dict[str, Any]: Task information
    """
    result = celery_app.AsyncResult(task_id)
    
    return {
        "task_id": task_id,
        "status": result.status,
        "result": result.result,
        "traceback": result.traceback,
        "date_done": result.date_done,
        "successful": result.successful(),
        "failed": result.failed(),
        "ready": result.ready(),
    }


def revoke_task(task_id: str, terminate: bool = False) -> bool:
    """
    Revoke a task.
    
    Args:
        task_id: Task ID
        terminate: Whether to terminate the task
        
    Returns:
        bool: True if task was revoked
    """
    try:
        celery_app.control.revoke(task_id, terminate=terminate)
        logger.info(f"Task {task_id} revoked (terminate={terminate})")
        return True
    except Exception as e:
        logger.error(f"Failed to revoke task {task_id}: {e}")
        return False


def get_active_tasks() -> Dict[str, Any]:
    """
    Get active tasks.
    
    Returns:
        Dict[str, Any]: Active tasks information
    """
    try:
        inspect = celery_app.control.inspect()
        active_tasks = inspect.active()
        return active_tasks or {}
    except Exception as e:
        logger.error(f"Failed to get active tasks: {e}")
        return {}


def get_worker_stats() -> Dict[str, Any]:
    """
    Get worker statistics.
    
    Returns:
        Dict[str, Any]: Worker statistics
    """
    try:
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        return stats or {}
    except Exception as e:
        logger.error(f"Failed to get worker stats: {e}")
        return {}


def purge_queue(queue_name: str) -> int:
    """
    Purge a queue.
    
    Args:
        queue_name: Queue name
        
    Returns:
        int: Number of messages purged
    """
    try:
        with celery_app.connection() as connection:
            queue = celery_app.amqp.queues[queue_name]
            return queue.purge()
    except Exception as e:
        logger.error(f"Failed to purge queue {queue_name}: {e}")
        return 0


# Health check task
@celery_app.task(bind=True)
def health_check(self):
    """Health check task."""
    return {
        "status": "healthy",
        "worker_id": self.request.hostname,
        "task_id": self.request.id,
        "timestamp": self.request.eta or "now"
    }
