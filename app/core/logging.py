"""
Logging configuration and utilities.
"""
import logging
import logging.config
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from app.core.config import settings


class ColoredFormatter(logging.Formatter):
    """Colored log formatter for console output."""
    
    # Color codes
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m'       # Reset
    }
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record with colors."""
        # Add color to level name
        level_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{level_color}{record.levelname}{self.COLORS['RESET']}"
        
        # Format the message
        formatted = super().format(record)
        
        return formatted


class TenantContextFilter(logging.Filter):
    """Filter to add tenant context to log records."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add tenant context to log record."""
        # Try to get tenant context from various sources
        tenant_id = getattr(record, 'tenant_id', None)
        user_id = getattr(record, 'user_id', None)
        user_type = getattr(record, 'user_type', None)
        
        # Add context fields
        record.tenant_id = tenant_id or 'N/A'
        record.user_id = user_id or 'N/A'
        record.user_type = user_type or 'N/A'
        
        return True


class RequestContextFilter(logging.Filter):
    """Filter to add request context to log records."""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Add request context to log record."""
        # Try to get request context
        request_id = getattr(record, 'request_id', None)
        ip_address = getattr(record, 'ip_address', None)
        method = getattr(record, 'method', None)
        path = getattr(record, 'path', None)
        
        # Add context fields
        record.request_id = request_id or 'N/A'
        record.ip_address = ip_address or 'N/A'
        record.method = method or 'N/A'
        record.path = path or 'N/A'
        
        return True


def get_logging_config() -> Dict[str, Any]:
    """
    Get logging configuration based on settings.
    
    Returns:
        Dict[str, Any]: Logging configuration
    """
    # Ensure logs directory exists
    log_dir = Path(settings.log_dir)
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # Base configuration
    config = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'default': {
                'format': '[{asctime}] [{levelname:8}] [{name}] {message}',
                'style': '{',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'detailed': {
                'format': '[{asctime}] [{levelname:8}] [{name}] [{funcName}:{lineno}] {message}',
                'style': '{',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'json': {
                'format': '{"timestamp": "%(asctime)s", "level": "%(levelname)s", "logger": "%(name)s", "message": "%(message)s"}',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            },
            'colored': {
                '()': ColoredFormatter,
                'format': '[{asctime}] [{levelname:8}] [{name}] {message}',
                'style': '{',
                'datefmt': '%Y-%m-%d %H:%M:%S'
            }
        },
        'filters': {
            'tenant_context': {
                '()': TenantContextFilter,
            },
            'request_context': {
                '()': RequestContextFilter,
            }
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'level': settings.log_level,
                'formatter': 'colored' if settings.log_colored else 'default',
                'stream': sys.stdout,
                'filters': ['tenant_context', 'request_context']
            },
            'file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'level': settings.log_level,
                'formatter': 'detailed',
                'filename': log_dir / 'app.log',
                'maxBytes': settings.log_max_size,
                'backupCount': settings.log_backup_count,
                'encoding': 'utf-8',
                'filters': ['tenant_context', 'request_context']
            },
            'error_file': {
                'class': 'logging.handlers.RotatingFileHandler',
                'level': 'ERROR',
                'formatter': 'detailed',
                'filename': log_dir / 'error.log',
                'maxBytes': settings.log_max_size,
                'backupCount': settings.log_backup_count,
                'encoding': 'utf-8',
                'filters': ['tenant_context', 'request_context']
            }
        },
        'loggers': {
            'app': {
                'level': settings.log_level,
                'handlers': ['console', 'file', 'error_file'],
                'propagate': False
            },
            'uvicorn': {
                'level': 'INFO',
                'handlers': ['console', 'file'],
                'propagate': False
            },
            'uvicorn.access': {
                'level': 'INFO',
                'handlers': ['file'],
                'propagate': False
            },
            'sqlalchemy.engine': {
                'level': 'WARNING' if not settings.debug else 'INFO',
                'handlers': ['file'],
                'propagate': False
            },
            'alembic': {
                'level': 'INFO',
                'handlers': ['console', 'file'],
                'propagate': False
            }
        },
        'root': {
            'level': settings.log_level,
            'handlers': ['console', 'file', 'error_file']
        }
    }
    
    # Add JSON handler if enabled
    if settings.log_json:
        config['handlers']['json_file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'level': settings.log_level,
            'formatter': 'json',
            'filename': log_dir / 'app.json',
            'maxBytes': settings.log_max_size,
            'backupCount': settings.log_backup_count,
            'encoding': 'utf-8',
            'filters': ['tenant_context', 'request_context']
        }
        
        # Add JSON handler to loggers
        for logger_config in config['loggers'].values():
            if 'json_file' not in logger_config['handlers']:
                logger_config['handlers'].append('json_file')
        
        config['root']['handlers'].append('json_file')
    
    # Add syslog handler if enabled
    if settings.log_syslog:
        config['handlers']['syslog'] = {
            'class': 'logging.handlers.SysLogHandler',
            'level': settings.log_level,
            'formatter': 'default',
            'address': ('localhost', 514),
            'filters': ['tenant_context', 'request_context']
        }
        
        # Add syslog handler to loggers
        for logger_config in config['loggers'].values():
            if 'syslog' not in logger_config['handlers']:
                logger_config['handlers'].append('syslog')
        
        config['root']['handlers'].append('syslog')
    
    return config


def setup_logging() -> None:
    """Setup logging configuration."""
    config = get_logging_config()
    logging.config.dictConfig(config)
    
    # Log startup message
    logger = logging.getLogger('app.core.logging')
    logger.info(f"Logging configured - Level: {settings.log_level}, Dir: {settings.log_dir}")


def get_logger(name: str) -> logging.Logger:
    """
    Get logger with app prefix.
    
    Args:
        name: Logger name
        
    Returns:
        logging.Logger: Configured logger
    """
    return logging.getLogger(f'app.{name}')


class LogContext:
    """Context manager for adding context to log records."""
    
    def __init__(self, **context):
        """
        Initialize log context.
        
        Args:
            **context: Context fields to add to log records
        """
        self.context = context
        self.old_factory = None
    
    def __enter__(self):
        """Enter context manager."""
        self.old_factory = logging.getLogRecordFactory()
        
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            for key, value in self.context.items():
                setattr(record, key, value)
            return record
        
        logging.setLogRecordFactory(record_factory)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context manager."""
        logging.setLogRecordFactory(self.old_factory)


def log_with_context(logger: logging.Logger, level: int, message: str, **context) -> None:
    """
    Log message with additional context.
    
    Args:
        logger: Logger instance
        level: Log level
        message: Log message
        **context: Additional context fields
    """
    with LogContext(**context):
        logger.log(level, message)


def log_exception(logger: logging.Logger, message: str, **context) -> None:
    """
    Log exception with context.
    
    Args:
        logger: Logger instance
        message: Log message
        **context: Additional context fields
    """
    with LogContext(**context):
        logger.exception(message)


# Convenience functions
def log_user_action(
    logger: logging.Logger,
    action: str,
    user_id: int,
    user_type: str,
    tenant_id: Optional[int] = None,
    details: Optional[str] = None
) -> None:
    """
    Log user action.
    
    Args:
        logger: Logger instance
        action: Action performed
        user_id: User ID
        user_type: User type (admin, tenant, user)
        tenant_id: Tenant ID
        details: Additional details
    """
    context = {
        'user_id': user_id,
        'user_type': user_type,
        'tenant_id': tenant_id or 'N/A',
        'action': action
    }
    
    message = f"User action: {action}"
    if details:
        message += f" - {details}"
    
    with LogContext(**context):
        logger.info(message)


def log_security_event(
    logger: logging.Logger,
    event_type: str,
    user_id: Optional[int] = None,
    ip_address: Optional[str] = None,
    details: Optional[str] = None
) -> None:
    """
    Log security event.
    
    Args:
        logger: Logger instance
        event_type: Type of security event
        user_id: User ID involved
        ip_address: IP address
        details: Additional details
    """
    context = {
        'user_id': user_id or 'N/A',
        'ip_address': ip_address or 'N/A',
        'event_type': event_type
    }
    
    message = f"Security event: {event_type}"
    if details:
        message += f" - {details}"
    
    with LogContext(**context):
        logger.warning(message)
