"""
Authentication dependencies and middleware.
"""
from typing import Optional

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>orizationCred<PERSON><PERSON>, HTTPBearer
from sqlalchemy.orm import Session

from app.core.security import get_tenant_id, get_token_subject, get_user_type, verify_token
from app.db.database import get_db
from app.models.user import Admin, Tenant, User

# HTTP Bearer token scheme
security = HTTPBearer()


class CurrentUser:
    """Current user information."""
    
    def __init__(
        self,
        id: int,
        user_type: str,
        tenant_id: Optional[int] = None,
        username: Optional[str] = None,
        email: Optional[str] = None,
        is_active: bool = True,
        permissions: Optional[list] = None
    ):
        self.id = id
        self.user_type = user_type
        self.tenant_id = tenant_id
        self.username = username
        self.email = email
        self.is_active = is_active
        self.permissions = permissions or []
    
    @property
    def is_admin(self) -> bool:
        """Check if user is admin."""
        return self.user_type == "admin"
    
    @property
    def is_tenant(self) -> bool:
        """Check if user is tenant."""
        return self.user_type == "tenant"
    
    @property
    def is_user(self) -> bool:
        """Check if user is regular user."""
        return self.user_type == "user"


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> CurrentUser:
    """
    Get current authenticated user.
    
    Args:
        credentials: HTTP authorization credentials
        db: Database session
        
    Returns:
        CurrentUser: Current user information
        
    Raises:
        HTTPException: If authentication fails
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    # Verify token
    payload = verify_token(credentials.credentials)
    if payload is None:
        raise credentials_exception
    
    # Get user information from token
    user_id = get_token_subject(payload)
    user_type = get_user_type(payload)
    tenant_id = get_tenant_id(payload)
    
    if user_id is None or user_type is None:
        raise credentials_exception
    
    try:
        user_id = int(user_id)
    except (ValueError, TypeError):
        raise credentials_exception
    
    # Get user from database based on type
    user_data = None
    if user_type == "admin":
        user_data = db.query(Admin).filter(Admin.id == user_id).first()
    elif user_type == "tenant":
        user_data = db.query(Tenant).filter(Tenant.id == user_id).first()
    elif user_type == "user":
        user_data = db.query(User).filter(User.id == user_id).first()
    
    if user_data is None:
        raise credentials_exception
    
    if not user_data.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user"
        )
    
    return CurrentUser(
        id=user_data.id,
        user_type=user_type,
        tenant_id=tenant_id,
        username=getattr(user_data, 'username', getattr(user_data, 'name', None)),
        email=user_data.email,
        is_active=user_data.is_active
    )


async def get_current_admin(
    current_user: CurrentUser = Depends(get_current_user)
) -> CurrentUser:
    """
    Get current admin user.
    
    Args:
        current_user: Current user
        
    Returns:
        CurrentUser: Current admin user
        
    Raises:
        HTTPException: If user is not admin
    """
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required"
        )
    return current_user


async def get_current_tenant(
    current_user: CurrentUser = Depends(get_current_user)
) -> CurrentUser:
    """
    Get current tenant user.
    
    Args:
        current_user: Current user
        
    Returns:
        CurrentUser: Current tenant user
        
    Raises:
        HTTPException: If user is not tenant
    """
    if not current_user.is_tenant:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Tenant access required"
        )
    return current_user


async def get_current_active_user(
    current_user: CurrentUser = Depends(get_current_user)
) -> CurrentUser:
    """
    Get current active user (any type).
    
    Args:
        current_user: Current user
        
    Returns:
        CurrentUser: Current active user
        
    Raises:
        HTTPException: If user is not active
    """
    if not current_user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Inactive user"
        )
    return current_user


def require_tenant_access(
    current_user: CurrentUser = Depends(get_current_user),
    required_tenant_id: Optional[int] = None
) -> CurrentUser:
    """
    Require tenant access for multi-tenant operations.
    
    Args:
        current_user: Current user
        required_tenant_id: Required tenant ID
        
    Returns:
        CurrentUser: Current user with tenant access
        
    Raises:
        HTTPException: If access is denied
    """
    # Admins can access any tenant
    if current_user.is_admin:
        return current_user
    
    # Tenants can only access their own data
    if current_user.is_tenant:
        if required_tenant_id and current_user.id != required_tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this tenant"
            )
        return current_user
    
    # Users can only access their own tenant's data
    if current_user.is_user:
        if required_tenant_id and current_user.tenant_id != required_tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied to this tenant"
            )
        return current_user
    
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="Access denied"
    )
