"""
RBAC permission system implementation.
"""
from enum import Enum
from typing import List, Optional, Set

from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.auth import CurrentUser, get_current_user
from app.db.database import get_db
from app.models.permission import (
    AdminPermission,
    AdminRole,
    Permission,
    PermissionType,
    ResourceType,
    Role,
    RolePermission,
    TenantPermission,
    TenantRole,
    UserPermission,
    UserRole,
)


class PermissionChecker:
    """Permission checker for RBAC system."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_user_permissions(self, user: CurrentUser) -> Set[str]:
        """
        Get all permissions for a user.
        
        Args:
            user: Current user
            
        Returns:
            Set[str]: Set of permission codes
        """
        permissions = set()
        
        if user.is_admin:
            # Get direct admin permissions
            admin_perms = self.db.query(AdminPermission).filter(
                AdminPermission.admin_id == user.id
            ).all()
            for perm in admin_perms:
                permission = self.db.query(Permission).filter(
                    Permission.id == perm.permission_id,
                    Permission.is_active == True
                ).first()
                if permission:
                    permissions.add(permission.code)
            
            # Get admin role permissions
            admin_roles = self.db.query(AdminRole).filter(
                AdminRole.admin_id == user.id
            ).all()
            for role_assoc in admin_roles:
                role = self.db.query(Role).filter(
                    Role.id == role_assoc.role_id,
                    Role.is_active == True
                ).first()
                if role:
                    role_perms = self.db.query(RolePermission).filter(
                        RolePermission.role_id == role.id
                    ).all()
                    for role_perm in role_perms:
                        permission = self.db.query(Permission).filter(
                            Permission.id == role_perm.permission_id,
                            Permission.is_active == True
                        ).first()
                        if permission:
                            permissions.add(permission.code)
        
        elif user.is_tenant:
            # Get direct tenant permissions
            tenant_perms = self.db.query(TenantPermission).filter(
                TenantPermission.tenant_id == user.id
            ).all()
            for perm in tenant_perms:
                permission = self.db.query(Permission).filter(
                    Permission.id == perm.permission_id,
                    Permission.is_active == True
                ).first()
                if permission:
                    permissions.add(permission.code)
            
            # Get tenant role permissions
            tenant_roles = self.db.query(TenantRole).filter(
                TenantRole.tenant_id == user.id
            ).all()
            for role_assoc in tenant_roles:
                role = self.db.query(Role).filter(
                    Role.id == role_assoc.role_id,
                    Role.is_active == True
                ).first()
                if role:
                    role_perms = self.db.query(RolePermission).filter(
                        RolePermission.role_id == role.id
                    ).all()
                    for role_perm in role_perms:
                        permission = self.db.query(Permission).filter(
                            Permission.id == role_perm.permission_id,
                            Permission.is_active == True
                        ).first()
                        if permission:
                            permissions.add(permission.code)
        
        elif user.is_user:
            # Get direct user permissions
            user_perms = self.db.query(UserPermission).filter(
                UserPermission.user_id == user.id
            ).all()
            for perm in user_perms:
                permission = self.db.query(Permission).filter(
                    Permission.id == perm.permission_id,
                    Permission.is_active == True
                ).first()
                if permission:
                    permissions.add(permission.code)
            
            # Get user role permissions
            user_roles = self.db.query(UserRole).filter(
                UserRole.user_id == user.id
            ).all()
            for role_assoc in user_roles:
                role = self.db.query(Role).filter(
                    Role.id == role_assoc.role_id,
                    Role.is_active == True
                ).first()
                if role:
                    role_perms = self.db.query(RolePermission).filter(
                        RolePermission.role_id == role.id
                    ).all()
                    for role_perm in role_perms:
                        permission = self.db.query(Permission).filter(
                            Permission.id == role_perm.permission_id,
                            Permission.is_active == True
                        ).first()
                        if permission:
                            permissions.add(permission.code)
        
        return permissions
    
    def has_permission(self, user: CurrentUser, permission_code: str) -> bool:
        """
        Check if user has a specific permission.
        
        Args:
            user: Current user
            permission_code: Permission code to check
            
        Returns:
            bool: True if user has permission
        """
        user_permissions = self.get_user_permissions(user)
        return permission_code in user_permissions
    
    def has_any_permission(self, user: CurrentUser, permission_codes: List[str]) -> bool:
        """
        Check if user has any of the specified permissions.
        
        Args:
            user: Current user
            permission_codes: List of permission codes
            
        Returns:
            bool: True if user has any permission
        """
        user_permissions = self.get_user_permissions(user)
        return any(code in user_permissions for code in permission_codes)
    
    def has_all_permissions(self, user: CurrentUser, permission_codes: List[str]) -> bool:
        """
        Check if user has all specified permissions.
        
        Args:
            user: Current user
            permission_codes: List of permission codes
            
        Returns:
            bool: True if user has all permissions
        """
        user_permissions = self.get_user_permissions(user)
        return all(code in user_permissions for code in permission_codes)
    
    def can_access_resource(
        self,
        user: CurrentUser,
        resource_type: ResourceType,
        permission_type: PermissionType,
        tenant_id: Optional[int] = None
    ) -> bool:
        """
        Check if user can access a resource with specific permission.
        
        Args:
            user: Current user
            resource_type: Type of resource
            permission_type: Type of permission
            tenant_id: Tenant ID for multi-tenant resources
            
        Returns:
            bool: True if user can access resource
        """
        # Admins have access to everything
        if user.is_admin:
            return True
        
        # Check tenant access for multi-tenant resources
        if tenant_id is not None:
            if user.is_tenant and user.id != tenant_id:
                return False
            if user.is_user and user.tenant_id != tenant_id:
                return False
        
        # Build permission code
        permission_code = f"{resource_type.value}:{permission_type.value}"
        
        return self.has_permission(user, permission_code)


def get_permission_checker(db: Session = Depends(get_db)) -> PermissionChecker:
    """Get permission checker instance."""
    return PermissionChecker(db)


def require_permission(permission_code: str):
    """
    Decorator to require specific permission.
    
    Args:
        permission_code: Required permission code
        
    Returns:
        Dependency function
    """
    def permission_dependency(
        current_user: CurrentUser = Depends(get_current_user),
        permission_checker: PermissionChecker = Depends(get_permission_checker)
    ) -> CurrentUser:
        if not permission_checker.has_permission(current_user, permission_code):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Permission required: {permission_code}"
            )
        return current_user
    
    return permission_dependency


def require_any_permission(permission_codes: List[str]):
    """
    Decorator to require any of the specified permissions.
    
    Args:
        permission_codes: List of permission codes
        
    Returns:
        Dependency function
    """
    def permission_dependency(
        current_user: CurrentUser = Depends(get_current_user),
        permission_checker: PermissionChecker = Depends(get_permission_checker)
    ) -> CurrentUser:
        if not permission_checker.has_any_permission(current_user, permission_codes):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"One of these permissions required: {', '.join(permission_codes)}"
            )
        return current_user
    
    return permission_dependency


def require_all_permissions(permission_codes: List[str]):
    """
    Decorator to require all specified permissions.
    
    Args:
        permission_codes: List of permission codes
        
    Returns:
        Dependency function
    """
    def permission_dependency(
        current_user: CurrentUser = Depends(get_current_user),
        permission_checker: PermissionChecker = Depends(get_permission_checker)
    ) -> CurrentUser:
        if not permission_checker.has_all_permissions(current_user, permission_codes):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"All these permissions required: {', '.join(permission_codes)}"
            )
        return current_user
    
    return permission_dependency


def require_resource_access(
    resource_type: ResourceType,
    permission_type: PermissionType,
    tenant_id: Optional[int] = None
):
    """
    Decorator to require resource access permission.
    
    Args:
        resource_type: Type of resource
        permission_type: Type of permission
        tenant_id: Tenant ID for multi-tenant resources
        
    Returns:
        Dependency function
    """
    def permission_dependency(
        current_user: CurrentUser = Depends(get_current_user),
        permission_checker: PermissionChecker = Depends(get_permission_checker)
    ) -> CurrentUser:
        if not permission_checker.can_access_resource(
            current_user, resource_type, permission_type, tenant_id
        ):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Access denied to {resource_type.value} with {permission_type.value} permission"
            )
        return current_user
    
    return permission_dependency
