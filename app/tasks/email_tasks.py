"""
Email-related Celery tasks.
"""
import logging
from typing import Dict, List, Optional

from app.core.celery_app import celery_app
from app.core.config import settings
from app.events.base import event_bus
from app.models.event import EventPriority

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="app.tasks.email_tasks.send_email")
def send_email_task(
    self,
    to_email: str,
    subject: str,
    body: str,
    from_email: Optional[str] = None,
    html_body: Optional[str] = None,
    attachments: Optional[List[Dict]] = None
):
    """
    Send single email.
    
    Args:
        to_email: Recipient email
        subject: Email subject
        body: Email body (plain text)
        from_email: Sender email
        html_body: HTML email body
        attachments: List of attachments
    """
    try:
        # Validate email settings
        if not settings.smtp_host:
            raise ValueError("SMTP settings not configured")
        
        # Prepare email data
        email_data = {
            "to": to_email,
            "from": from_email or f"noreply@{settings.app_name.lower()}.com",
            "subject": subject,
            "body": body,
            "html_body": html_body,
            "attachments": attachments or []
        }
        
        # Send email (placeholder - implement actual email sending)
        # This would typically use libraries like:
        # - smtplib for basic SMTP
        # - sendgrid for SendGrid API
        # - boto3 for AWS SES
        # - requests for other email APIs
        
        logger.info(f"Sending email to {to_email} with subject: {subject}")
        
        # Simulate email sending
        import time
        time.sleep(1)  # Simulate network delay
        
        # Publish event
        event_bus.publish(
            event_type="email.sent",
            data={
                "to_email": to_email,
                "subject": subject,
                "from_email": email_data["from"],
                "has_attachments": len(email_data["attachments"]) > 0
            },
            priority=EventPriority.NORMAL
        )
        
        return {
            "status": "success",
            "message": f"Email sent to {to_email}",
            "to_email": to_email,
            "subject": subject
        }
        
    except Exception as e:
        logger.error(f"Failed to send email to {to_email}: {e}")
        
        # Publish failure event
        event_bus.publish(
            event_type="email.failed",
            data={
                "to_email": to_email,
                "subject": subject,
                "error": str(e)
            },
            priority=EventPriority.HIGH
        )
        
        raise self.retry(exc=e, countdown=60, max_retries=3)


@celery_app.task(bind=True, name="app.tasks.email_tasks.send_bulk_email")
def send_bulk_email_task(
    self,
    recipients: List[str],
    subject: str,
    body: str,
    from_email: Optional[str] = None,
    html_body: Optional[str] = None,
    batch_size: int = 10
):
    """
    Send bulk email to multiple recipients.
    
    Args:
        recipients: List of recipient emails
        subject: Email subject
        body: Email body (plain text)
        from_email: Sender email
        html_body: HTML email body
        batch_size: Number of emails to send per batch
    """
    try:
        total_recipients = len(recipients)
        sent_count = 0
        failed_count = 0
        
        # Process recipients in batches
        for i in range(0, total_recipients, batch_size):
            batch = recipients[i:i + batch_size]
            
            for email in batch:
                try:
                    # Send individual email
                    send_email_task.delay(
                        to_email=email,
                        subject=subject,
                        body=body,
                        from_email=from_email,
                        html_body=html_body
                    )
                    sent_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to queue email for {email}: {e}")
                    failed_count += 1
            
            # Small delay between batches to avoid overwhelming the system
            import time
            time.sleep(0.1)
        
        logger.info(f"Bulk email queued: {sent_count} sent, {failed_count} failed")
        
        # Publish event
        event_bus.publish(
            event_type="email.bulk.queued",
            data={
                "total_recipients": total_recipients,
                "sent_count": sent_count,
                "failed_count": failed_count,
                "subject": subject
            },
            priority=EventPriority.NORMAL
        )
        
        return {
            "status": "success",
            "message": f"Bulk email queued for {total_recipients} recipients",
            "total_recipients": total_recipients,
            "sent_count": sent_count,
            "failed_count": failed_count
        }
        
    except Exception as e:
        logger.error(f"Failed to send bulk email: {e}")
        raise self.retry(exc=e, countdown=120, max_retries=2)


@celery_app.task(bind=True, name="app.tasks.email_tasks.send_notification_email")
def send_notification_email_task(
    self,
    user_id: int,
    user_type: str,
    notification_type: str,
    data: Dict,
    template: Optional[str] = None
):
    """
    Send notification email to user.
    
    Args:
        user_id: User ID
        user_type: User type (admin, tenant, user)
        notification_type: Type of notification
        data: Notification data
        template: Email template name
    """
    try:
        from app.db.database import SessionLocal
        from app.services.user import admin_service, tenant_service, user_service
        
        db = SessionLocal()
        
        # Get user information
        if user_type == "admin":
            user = admin_service.get(db, user_id)
        elif user_type == "tenant":
            user = tenant_service.get(db, user_id)
        elif user_type == "user":
            user = user_service.get(db, user_id)
        else:
            raise ValueError(f"Invalid user type: {user_type}")
        
        if not user:
            raise ValueError(f"User not found: {user_id}")
        
        # Prepare notification email
        template_name = template or f"notification_{notification_type}"
        
        # Get email content based on notification type
        email_content = get_notification_email_content(
            notification_type=notification_type,
            user=user,
            data=data
        )
        
        # Send email
        send_email_task.delay(
            to_email=user.email,
            subject=email_content["subject"],
            body=email_content["body"],
            html_body=email_content.get("html_body")
        )
        
        logger.info(f"Notification email queued for {user_type} {user_id}: {notification_type}")
        
        # Publish event
        event_bus.publish(
            event_type="email.notification.queued",
            data={
                "user_id": user_id,
                "user_type": user_type,
                "notification_type": notification_type,
                "email": user.email
            },
            priority=EventPriority.NORMAL
        )
        
        return {
            "status": "success",
            "message": f"Notification email queued for {user_type} {user_id}",
            "user_id": user_id,
            "user_type": user_type,
            "notification_type": notification_type
        }
        
    except Exception as e:
        logger.error(f"Failed to send notification email: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


def get_notification_email_content(notification_type: str, user, data: Dict) -> Dict[str, str]:
    """
    Get email content for notification type.
    
    Args:
        notification_type: Type of notification
        user: User object
        data: Notification data
        
    Returns:
        Dict[str, str]: Email content with subject and body
    """
    user_name = getattr(user, 'full_name', None) or getattr(user, 'name', None) or user.username
    
    templates = {
        "account_locked": {
            "subject": "Account Security Alert - Account Locked",
            "body": f"Hello {user_name},\n\nYour account has been locked due to security reasons. Please contact support for assistance.\n\nBest regards,\nThe {settings.app_name} Team"
        },
        "password_changed": {
            "subject": "Password Changed Successfully",
            "body": f"Hello {user_name},\n\nYour password has been changed successfully. If you did not make this change, please contact support immediately.\n\nBest regards,\nThe {settings.app_name} Team"
        },
        "login_alert": {
            "subject": "New Login Alert",
            "body": f"Hello {user_name},\n\nA new login to your account was detected from {data.get('ip_address', 'unknown location')} at {data.get('login_time', 'unknown time')}.\n\nIf this was not you, please secure your account immediately.\n\nBest regards,\nThe {settings.app_name} Team"
        },
        "tenant_created": {
            "subject": "New Tenant Account Created",
            "body": f"Hello {user_name},\n\nA new tenant account has been created: {data.get('tenant_name', 'Unknown')}.\n\nYou can now start managing your users and resources.\n\nBest regards,\nThe {settings.app_name} Team"
        },
        "user_invited": {
            "subject": "You've been invited to join",
            "body": f"Hello {user_name},\n\nYou have been invited to join {data.get('tenant_name', 'a tenant')} on {settings.app_name}.\n\nPlease log in to accept the invitation.\n\nBest regards,\nThe {settings.app_name} Team"
        }
    }
    
    return templates.get(notification_type, {
        "subject": "Notification",
        "body": f"Hello {user_name},\n\nYou have a new notification.\n\nBest regards,\nThe {settings.app_name} Team"
    })


@celery_app.task(bind=True, name="app.tasks.email_tasks.send_system_alert")
def send_system_alert_task(
    self,
    alert_type: str,
    message: str,
    severity: str = "info",
    recipients: Optional[List[str]] = None
):
    """
    Send system alert email to administrators.
    
    Args:
        alert_type: Type of alert
        message: Alert message
        severity: Alert severity (info, warning, error, critical)
        recipients: List of recipient emails (defaults to admin emails)
    """
    try:
        # Default recipients (system administrators)
        if not recipients:
            recipients = ["<EMAIL>"]  # Replace with actual admin emails
        
        # Prepare alert email
        subject = f"[{severity.upper()}] System Alert: {alert_type}"
        body = f"""
System Alert Notification

Alert Type: {alert_type}
Severity: {severity.upper()}
Time: {datetime.utcnow().isoformat()}

Message:
{message}

This is an automated alert from {settings.app_name}.
"""
        
        # Send to all recipients
        for email in recipients:
            send_email_task.delay(
                to_email=email,
                subject=subject,
                body=body
            )
        
        logger.info(f"System alert sent: {alert_type} ({severity})")
        
        # Publish event
        event_bus.publish(
            event_type="email.system_alert.sent",
            data={
                "alert_type": alert_type,
                "severity": severity,
                "recipients_count": len(recipients)
            },
            priority=EventPriority.HIGH if severity in ["error", "critical"] else EventPriority.NORMAL
        )
        
        return {
            "status": "success",
            "message": f"System alert sent to {len(recipients)} recipients",
            "alert_type": alert_type,
            "severity": severity,
            "recipients_count": len(recipients)
        }
        
    except Exception as e:
        logger.error(f"Failed to send system alert: {e}")
        raise self.retry(exc=e, countdown=30, max_retries=5)
