"""
Cleanup-related Celery tasks.
"""
import logging
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List

from sqlalchemy import text

from app.core.celery_app import celery_app
from app.core.config import settings
from app.db.database import SessionLocal
from app.events.base import event_bus
from app.models.event import EventPriority

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="app.tasks.cleanup_tasks.cleanup_expired_tokens")
def cleanup_expired_tokens_task(self):
    """
    Cleanup expired JWT tokens and sessions.
    """
    try:
        db = SessionLocal()
        
        # Cleanup expired refresh tokens (if stored in database)
        # This is a placeholder - implement based on your token storage strategy
        
        cutoff_time = datetime.utcnow() - timedelta(days=settings.refresh_token_expire_days)
        
        # Example cleanup query (adjust based on your token storage)
        result = db.execute(
            text("DELETE FROM refresh_tokens WHERE created_at < :cutoff_time"),
            {"cutoff_time": cutoff_time}
        )
        
        deleted_count = result.rowcount
        db.commit()
        
        logger.info(f"Cleaned up {deleted_count} expired tokens")
        
        # Publish event
        event_bus.publish(
            event_type="cleanup.tokens.completed",
            data={
                "deleted_count": deleted_count,
                "cutoff_time": cutoff_time.isoformat()
            },
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"Cleaned up {deleted_count} expired tokens",
            "deleted_count": deleted_count
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup expired tokens: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.cleanup_tasks.cleanup_old_logs")
def cleanup_old_logs_task(self, days_to_keep: int = 30):
    """
    Cleanup old log files.
    
    Args:
        days_to_keep: Number of days to keep log files
    """
    try:
        log_dir = Path(settings.log_dir)
        
        if not log_dir.exists():
            logger.warning(f"Log directory does not exist: {log_dir}")
            return {
                "status": "success",
                "message": "Log directory does not exist",
                "deleted_count": 0
            }
        
        cutoff_time = datetime.utcnow() - timedelta(days=days_to_keep)
        deleted_count = 0
        total_size_freed = 0
        
        # Find and delete old log files
        for log_file in log_dir.glob("*.log*"):
            try:
                # Get file modification time
                file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                
                if file_mtime < cutoff_time:
                    file_size = log_file.stat().st_size
                    log_file.unlink()
                    deleted_count += 1
                    total_size_freed += file_size
                    logger.debug(f"Deleted old log file: {log_file}")
                    
            except Exception as e:
                logger.error(f"Failed to delete log file {log_file}: {e}")
        
        # Convert bytes to MB for reporting
        size_mb = total_size_freed / (1024 * 1024)
        
        logger.info(f"Cleaned up {deleted_count} old log files, freed {size_mb:.2f} MB")
        
        # Publish event
        event_bus.publish(
            event_type="cleanup.logs.completed",
            data={
                "deleted_count": deleted_count,
                "size_freed_mb": round(size_mb, 2),
                "days_to_keep": days_to_keep
            },
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"Cleaned up {deleted_count} old log files",
            "deleted_count": deleted_count,
            "size_freed_mb": round(size_mb, 2)
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup old logs: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)


@celery_app.task(bind=True, name="app.tasks.cleanup_tasks.cleanup_temp_files")
def cleanup_temp_files_task(self, temp_dir: str = "/tmp", hours_to_keep: int = 24):
    """
    Cleanup temporary files.
    
    Args:
        temp_dir: Temporary directory path
        hours_to_keep: Number of hours to keep temp files
    """
    try:
        temp_path = Path(temp_dir)
        
        if not temp_path.exists():
            logger.warning(f"Temp directory does not exist: {temp_path}")
            return {
                "status": "success",
                "message": "Temp directory does not exist",
                "deleted_count": 0
            }
        
        cutoff_time = datetime.utcnow() - timedelta(hours=hours_to_keep)
        deleted_count = 0
        total_size_freed = 0
        
        # Find and delete old temp files (only app-related files)
        app_temp_pattern = f"{settings.app_name.lower()}_*"
        
        for temp_file in temp_path.glob(app_temp_pattern):
            try:
                # Get file modification time
                file_mtime = datetime.fromtimestamp(temp_file.stat().st_mtime)
                
                if file_mtime < cutoff_time:
                    if temp_file.is_file():
                        file_size = temp_file.stat().st_size
                        temp_file.unlink()
                        total_size_freed += file_size
                    elif temp_file.is_dir():
                        import shutil
                        dir_size = sum(f.stat().st_size for f in temp_file.rglob('*') if f.is_file())
                        shutil.rmtree(temp_file)
                        total_size_freed += dir_size
                    
                    deleted_count += 1
                    logger.debug(f"Deleted temp file/dir: {temp_file}")
                    
            except Exception as e:
                logger.error(f"Failed to delete temp file {temp_file}: {e}")
        
        # Convert bytes to MB for reporting
        size_mb = total_size_freed / (1024 * 1024)
        
        logger.info(f"Cleaned up {deleted_count} temp files, freed {size_mb:.2f} MB")
        
        # Publish event
        event_bus.publish(
            event_type="cleanup.temp_files.completed",
            data={
                "deleted_count": deleted_count,
                "size_freed_mb": round(size_mb, 2),
                "hours_to_keep": hours_to_keep
            },
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"Cleaned up {deleted_count} temp files",
            "deleted_count": deleted_count,
            "size_freed_mb": round(size_mb, 2)
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup temp files: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)


@celery_app.task(bind=True, name="app.tasks.cleanup_tasks.cleanup_old_events")
def cleanup_old_events_task(self, days_to_keep: int = 90):
    """
    Cleanup old event logs.
    
    Args:
        days_to_keep: Number of days to keep event logs
    """
    try:
        db = SessionLocal()
        
        cutoff_time = datetime.utcnow() - timedelta(days=days_to_keep)
        
        # Delete old event logs
        result = db.execute(
            text("DELETE FROM event_logs WHERE created_at < :cutoff_time"),
            {"cutoff_time": cutoff_time}
        )
        
        deleted_count = result.rowcount
        db.commit()
        
        logger.info(f"Cleaned up {deleted_count} old event logs")
        
        # Publish event
        event_bus.publish(
            event_type="cleanup.events.completed",
            data={
                "deleted_count": deleted_count,
                "days_to_keep": days_to_keep,
                "cutoff_time": cutoff_time.isoformat()
            },
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"Cleaned up {deleted_count} old event logs",
            "deleted_count": deleted_count
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup old events: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.cleanup_tasks.cleanup_database")
def cleanup_database_task(self):
    """
    Perform database maintenance and cleanup.
    """
    try:
        db = SessionLocal()
        
        maintenance_tasks = []
        
        # Optimize tables (MySQL specific)
        tables_to_optimize = [
            "admins", "tenants", "users", "permissions", "roles",
            "events", "event_logs", "event_subscriptions"
        ]
        
        for table in tables_to_optimize:
            try:
                db.execute(text(f"OPTIMIZE TABLE {table}"))
                maintenance_tasks.append(f"Optimized table: {table}")
            except Exception as e:
                logger.error(f"Failed to optimize table {table}: {e}")
                maintenance_tasks.append(f"Failed to optimize table: {table}")
        
        # Update table statistics
        try:
            db.execute(text("ANALYZE TABLE admins, tenants, users, permissions, roles"))
            maintenance_tasks.append("Updated table statistics")
        except Exception as e:
            logger.error(f"Failed to update table statistics: {e}")
            maintenance_tasks.append("Failed to update table statistics")
        
        db.commit()
        
        logger.info(f"Database maintenance completed: {len(maintenance_tasks)} tasks")
        
        # Publish event
        event_bus.publish(
            event_type="cleanup.database.completed",
            data={
                "tasks_completed": len(maintenance_tasks),
                "tasks": maintenance_tasks
            },
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"Database maintenance completed: {len(maintenance_tasks)} tasks",
            "tasks": maintenance_tasks
        }
        
    except Exception as e:
        logger.error(f"Failed to perform database cleanup: {e}")
        raise self.retry(exc=e, countdown=600, max_retries=1)
    finally:
        db.close()
