"""
User-related Celery tasks.
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from sqlalchemy.orm import Session

from app.core.celery_app import celery_app
from app.db.database import SessionLocal
from app.events.base import event_bus
from app.models.event import EventPriority
from app.services.user import admin_service, tenant_service, user_service

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="app.tasks.user_tasks.send_welcome_email")
def send_welcome_email_task(self, user_id: int, user_type: str, tenant_id: Optional[int] = None):
    """
    Send welcome email to new user.
    
    Args:
        user_id: User ID
        user_type: User type (admin, tenant, user)
        tenant_id: Tenant ID (for users)
    """
    try:
        db = SessionLocal()
        
        # Get user information
        if user_type == "admin":
            user = admin_service.get(db, user_id)
            email_template = "admin_welcome"
        elif user_type == "tenant":
            user = tenant_service.get(db, user_id)
            email_template = "tenant_welcome"
        elif user_type == "user":
            user = user_service.get(db, user_id)
            email_template = "user_welcome"
        else:
            raise ValueError(f"Invalid user type: {user_type}")
        
        if not user:
            raise ValueError(f"User not found: {user_id}")
        
        # Prepare email data
        email_data = {
            "to_email": user.email,
            "template": email_template,
            "context": {
                "user_name": getattr(user, 'full_name', None) or getattr(user, 'name', None) or user.username,
                "user_type": user_type,
                "login_url": "https://your-app.com/login",  # Replace with actual URL
            }
        }
        
        # Send email (placeholder - implement actual email sending)
        logger.info(f"Sending welcome email to {user.email} for {user_type} {user_id}")
        
        # Publish event
        event_bus.publish(
            event_type="email.welcome.sent",
            data={
                "user_id": user_id,
                "user_type": user_type,
                "tenant_id": tenant_id,
                "email": user.email,
                "template": email_template
            },
            priority=EventPriority.NORMAL
        )
        
        return {
            "status": "success",
            "message": f"Welcome email sent to {user.email}",
            "user_id": user_id,
            "user_type": user_type
        }
        
    except Exception as e:
        logger.error(f"Failed to send welcome email: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.user_tasks.update_user_last_login")
def update_user_last_login_task(self, user_id: int, user_type: str, login_time: Optional[str] = None):
    """
    Update user's last login time.
    
    Args:
        user_id: User ID
        user_type: User type (admin, tenant, user)
        login_time: Login time (ISO format)
    """
    try:
        db = SessionLocal()
        
        # Parse login time
        if login_time:
            last_login = datetime.fromisoformat(login_time)
        else:
            last_login = datetime.utcnow()
        
        # Update last login based on user type
        if user_type == "admin":
            admin_service.update(
                db,
                id=user_id,
                obj_in={"last_login": last_login}
            )
        elif user_type == "tenant":
            tenant_service.update(
                db,
                id=user_id,
                obj_in={"last_login": last_login}
            )
        elif user_type == "user":
            user_service.update(
                db,
                id=user_id,
                obj_in={"last_login": last_login}
            )
        else:
            raise ValueError(f"Invalid user type: {user_type}")
        
        logger.info(f"Updated last login for {user_type} {user_id}")
        
        return {
            "status": "success",
            "message": f"Last login updated for {user_type} {user_id}",
            "user_id": user_id,
            "user_type": user_type,
            "last_login": last_login.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to update last login: {e}")
        raise self.retry(exc=e, countdown=30, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.user_tasks.cleanup_inactive_users")
def cleanup_inactive_users_task(self, days_inactive: int = 90):
    """
    Cleanup inactive users.
    
    Args:
        days_inactive: Number of days to consider a user inactive
    """
    try:
        db = SessionLocal()
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_inactive)
        
        # Find inactive users
        inactive_users = user_service.dao.get_inactive_users(db, cutoff_date=cutoff_date)
        
        cleanup_count = 0
        for user in inactive_users:
            try:
                # Deactivate user instead of deleting
                user_service.update(
                    db,
                    id=user.id,
                    obj_in={"is_active": False, "status": "inactive"}
                )
                cleanup_count += 1
                
                # Publish event
                event_bus.publish(
                    event_type="user.deactivated.inactive",
                    data={
                        "user_id": user.id,
                        "tenant_id": user.tenant_id,
                        "last_login": user.last_login.isoformat() if user.last_login else None,
                        "days_inactive": days_inactive
                    },
                    priority=EventPriority.LOW
                )
                
            except Exception as e:
                logger.error(f"Failed to cleanup user {user.id}: {e}")
        
        logger.info(f"Cleaned up {cleanup_count} inactive users")
        
        return {
            "status": "success",
            "message": f"Cleaned up {cleanup_count} inactive users",
            "cleanup_count": cleanup_count,
            "days_inactive": days_inactive
        }
        
    except Exception as e:
        logger.error(f"Failed to cleanup inactive users: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.user_tasks.send_password_reset_email")
def send_password_reset_email_task(self, user_id: int, user_type: str, reset_token: str):
    """
    Send password reset email.
    
    Args:
        user_id: User ID
        user_type: User type
        reset_token: Password reset token
    """
    try:
        db = SessionLocal()
        
        # Get user information
        if user_type == "admin":
            user = admin_service.get(db, user_id)
        elif user_type == "tenant":
            user = tenant_service.get(db, user_id)
        elif user_type == "user":
            user = user_service.get(db, user_id)
        else:
            raise ValueError(f"Invalid user type: {user_type}")
        
        if not user:
            raise ValueError(f"User not found: {user_id}")
        
        # Prepare email data
        email_data = {
            "to_email": user.email,
            "template": "password_reset",
            "context": {
                "user_name": getattr(user, 'full_name', None) or getattr(user, 'name', None) or user.username,
                "reset_url": f"https://your-app.com/reset-password?token={reset_token}",  # Replace with actual URL
                "expires_in": "24 hours"
            }
        }
        
        # Send email (placeholder - implement actual email sending)
        logger.info(f"Sending password reset email to {user.email} for {user_type} {user_id}")
        
        # Publish event
        event_bus.publish(
            event_type="email.password_reset.sent",
            data={
                "user_id": user_id,
                "user_type": user_type,
                "email": user.email,
                "reset_token": reset_token
            },
            priority=EventPriority.HIGH
        )
        
        return {
            "status": "success",
            "message": f"Password reset email sent to {user.email}",
            "user_id": user_id,
            "user_type": user_type
        }
        
    except Exception as e:
        logger.error(f"Failed to send password reset email: {e}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.user_tasks.sync_user_permissions")
def sync_user_permissions_task(self, user_id: int, user_type: str):
    """
    Sync user permissions with external systems.
    
    Args:
        user_id: User ID
        user_type: User type
    """
    try:
        db = SessionLocal()
        
        # Get user information
        if user_type == "admin":
            user = admin_service.get(db, user_id)
        elif user_type == "tenant":
            user = tenant_service.get(db, user_id)
        elif user_type == "user":
            user = user_service.get(db, user_id)
        else:
            raise ValueError(f"Invalid user type: {user_type}")
        
        if not user:
            raise ValueError(f"User not found: {user_id}")
        
        # Sync permissions (placeholder - implement actual sync logic)
        logger.info(f"Syncing permissions for {user_type} {user_id}")
        
        # Publish event
        event_bus.publish(
            event_type="user.permissions.synced",
            data={
                "user_id": user_id,
                "user_type": user_type,
                "sync_time": datetime.utcnow().isoformat()
            },
            priority=EventPriority.NORMAL
        )
        
        return {
            "status": "success",
            "message": f"Permissions synced for {user_type} {user_id}",
            "user_id": user_id,
            "user_type": user_type
        }
        
    except Exception as e:
        logger.error(f"Failed to sync user permissions: {e}")
        raise self.retry(exc=e, countdown=120, max_retries=2)
    finally:
        db.close()
