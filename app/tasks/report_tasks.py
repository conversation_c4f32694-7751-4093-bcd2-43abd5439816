"""
Report generation Celery tasks.
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional

from sqlalchemy import func, text

from app.core.celery_app import celery_app
from app.db.database import SessionLocal
from app.events.base import event_bus
from app.models.event import EventPriority
from app.models.user import Admin, Tenant, User

logger = logging.getLogger(__name__)


@celery_app.task(bind=True, name="app.tasks.report_tasks.generate_daily_reports")
def generate_daily_reports_task(self, report_date: Optional[str] = None):
    """
    Generate daily reports.
    
    Args:
        report_date: Date for the report (ISO format, defaults to yesterday)
    """
    try:
        # Parse report date
        if report_date:
            target_date = datetime.fromisoformat(report_date).date()
        else:
            target_date = (datetime.utcnow() - timedelta(days=1)).date()
        
        db = SessionLocal()
        
        # Generate user activity report
        user_activity = generate_user_activity_report_task.delay(target_date.isoformat())
        
        # Generate tenant usage report
        tenant_usage = generate_tenant_usage_report_task.delay(target_date.isoformat())
        
        # Generate system metrics report
        system_metrics = generate_system_metrics_report_task.delay(target_date.isoformat())
        
        logger.info(f"Daily reports queued for {target_date}")
        
        # Publish event
        event_bus.publish(
            event_type="reports.daily.queued",
            data={
                "report_date": target_date.isoformat(),
                "reports": ["user_activity", "tenant_usage", "system_metrics"]
            },
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"Daily reports queued for {target_date}",
            "report_date": target_date.isoformat(),
            "task_ids": {
                "user_activity": user_activity.id,
                "tenant_usage": tenant_usage.id,
                "system_metrics": system_metrics.id
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to generate daily reports: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.report_tasks.generate_user_activity_report")
def generate_user_activity_report_task(self, report_date: str):
    """
    Generate user activity report.
    
    Args:
        report_date: Date for the report (ISO format)
    """
    try:
        target_date = datetime.fromisoformat(report_date).date()
        start_time = datetime.combine(target_date, datetime.min.time())
        end_time = datetime.combine(target_date, datetime.max.time())
        
        db = SessionLocal()
        
        # Get user statistics
        total_users = db.query(User).count()
        active_users = db.query(User).filter(User.is_active == True).count()
        
        # Get login statistics (placeholder - implement based on your login tracking)
        login_stats = db.execute(
            text("""
                SELECT 
                    COUNT(*) as total_logins,
                    COUNT(DISTINCT user_id) as unique_users
                FROM event_logs 
                WHERE event_type = 'user.login.success' 
                AND created_at BETWEEN :start_time AND :end_time
            """),
            {"start_time": start_time, "end_time": end_time}
        ).fetchone()
        
        # Get new user registrations
        new_users = db.query(User).filter(
            User.created_at >= start_time,
            User.created_at <= end_time
        ).count()
        
        # Generate report data
        report_data = {
            "report_date": report_date,
            "report_type": "user_activity",
            "generated_at": datetime.utcnow().isoformat(),
            "data": {
                "total_users": total_users,
                "active_users": active_users,
                "inactive_users": total_users - active_users,
                "new_users": new_users,
                "total_logins": login_stats.total_logins if login_stats else 0,
                "unique_login_users": login_stats.unique_users if login_stats else 0,
                "activity_rate": round((login_stats.unique_users / total_users * 100) if total_users > 0 and login_stats else 0, 2)
            }
        }
        
        # Save report (placeholder - implement report storage)
        logger.info(f"User activity report generated for {report_date}")
        
        # Publish event
        event_bus.publish(
            event_type="reports.user_activity.generated",
            data=report_data,
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"User activity report generated for {report_date}",
            "report_data": report_data
        }
        
    except Exception as e:
        logger.error(f"Failed to generate user activity report: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.report_tasks.generate_tenant_usage_report")
def generate_tenant_usage_report_task(self, report_date: str):
    """
    Generate tenant usage report.
    
    Args:
        report_date: Date for the report (ISO format)
    """
    try:
        target_date = datetime.fromisoformat(report_date).date()
        start_time = datetime.combine(target_date, datetime.min.time())
        end_time = datetime.combine(target_date, datetime.max.time())
        
        db = SessionLocal()
        
        # Get tenant statistics
        total_tenants = db.query(Tenant).count()
        active_tenants = db.query(Tenant).filter(Tenant.is_active == True).count()
        
        # Get tenant user counts
        tenant_user_stats = db.query(
            Tenant.id,
            Tenant.name,
            func.count(User.id).label('user_count')
        ).outerjoin(User).group_by(Tenant.id, Tenant.name).all()
        
        # Get tenant activity (placeholder - implement based on your activity tracking)
        tenant_activity = db.execute(
            text("""
                SELECT 
                    tenant_id,
                    COUNT(*) as activity_count
                FROM event_logs 
                WHERE created_at BETWEEN :start_time AND :end_time
                AND tenant_id IS NOT NULL
                GROUP BY tenant_id
            """),
            {"start_time": start_time, "end_time": end_time}
        ).fetchall()
        
        # Process tenant data
        tenant_data = []
        activity_dict = {row.tenant_id: row.activity_count for row in tenant_activity}
        
        for tenant_stat in tenant_user_stats:
            tenant_data.append({
                "tenant_id": tenant_stat.id,
                "tenant_name": tenant_stat.name,
                "user_count": tenant_stat.user_count,
                "activity_count": activity_dict.get(tenant_stat.id, 0)
            })
        
        # Generate report data
        report_data = {
            "report_date": report_date,
            "report_type": "tenant_usage",
            "generated_at": datetime.utcnow().isoformat(),
            "data": {
                "total_tenants": total_tenants,
                "active_tenants": active_tenants,
                "inactive_tenants": total_tenants - active_tenants,
                "tenant_details": tenant_data,
                "average_users_per_tenant": round(sum(t["user_count"] for t in tenant_data) / len(tenant_data) if tenant_data else 0, 2),
                "total_tenant_activity": sum(t["activity_count"] for t in tenant_data)
            }
        }
        
        # Save report (placeholder - implement report storage)
        logger.info(f"Tenant usage report generated for {report_date}")
        
        # Publish event
        event_bus.publish(
            event_type="reports.tenant_usage.generated",
            data=report_data,
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"Tenant usage report generated for {report_date}",
            "report_data": report_data
        }
        
    except Exception as e:
        logger.error(f"Failed to generate tenant usage report: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.report_tasks.generate_system_metrics_report")
def generate_system_metrics_report_task(self, report_date: str):
    """
    Generate system metrics report.
    
    Args:
        report_date: Date for the report (ISO format)
    """
    try:
        target_date = datetime.fromisoformat(report_date).date()
        start_time = datetime.combine(target_date, datetime.min.time())
        end_time = datetime.combine(target_date, datetime.max.time())
        
        db = SessionLocal()
        
        # Get event statistics
        event_stats = db.execute(
            text("""
                SELECT 
                    event_type,
                    COUNT(*) as count
                FROM event_logs 
                WHERE created_at BETWEEN :start_time AND :end_time
                GROUP BY event_type
                ORDER BY count DESC
            """),
            {"start_time": start_time, "end_time": end_time}
        ).fetchall()
        
        # Get error statistics
        error_stats = db.execute(
            text("""
                SELECT 
                    COUNT(*) as total_errors
                FROM event_logs 
                WHERE created_at BETWEEN :start_time AND :end_time
                AND (event_type LIKE '%.error%' OR event_type LIKE '%.failed%')
            """),
            {"start_time": start_time, "end_time": end_time}
        ).fetchone()
        
        # Get performance metrics (placeholder - implement based on your metrics collection)
        performance_metrics = {
            "avg_response_time": 0.0,  # Implement actual metrics
            "total_requests": 0,
            "error_rate": 0.0
        }
        
        # Process event data
        event_data = [{"event_type": row.event_type, "count": row.count} for row in event_stats]
        total_events = sum(row.count for row in event_stats)
        
        # Generate report data
        report_data = {
            "report_date": report_date,
            "report_type": "system_metrics",
            "generated_at": datetime.utcnow().isoformat(),
            "data": {
                "total_events": total_events,
                "total_errors": error_stats.total_errors if error_stats else 0,
                "error_rate": round((error_stats.total_errors / total_events * 100) if total_events > 0 and error_stats else 0, 2),
                "event_breakdown": event_data[:10],  # Top 10 event types
                "performance_metrics": performance_metrics
            }
        }
        
        # Save report (placeholder - implement report storage)
        logger.info(f"System metrics report generated for {report_date}")
        
        # Publish event
        event_bus.publish(
            event_type="reports.system_metrics.generated",
            data=report_data,
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"System metrics report generated for {report_date}",
            "report_data": report_data
        }
        
    except Exception as e:
        logger.error(f"Failed to generate system metrics report: {e}")
        raise self.retry(exc=e, countdown=300, max_retries=2)
    finally:
        db.close()


@celery_app.task(bind=True, name="app.tasks.report_tasks.export_report")
def export_report_task(self, report_data: Dict, format: str = "json", destination: str = "file"):
    """
    Export report to specified format and destination.
    
    Args:
        report_data: Report data to export
        format: Export format (json, csv, excel)
        destination: Export destination (file, email, s3)
    """
    try:
        import json
        from pathlib import Path
        
        # Create reports directory
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)
        
        # Generate filename
        report_type = report_data.get("report_type", "unknown")
        report_date = report_data.get("report_date", datetime.utcnow().date().isoformat())
        filename = f"{report_type}_{report_date}.{format}"
        filepath = reports_dir / filename
        
        # Export based on format
        if format == "json":
            with open(filepath, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
        
        elif format == "csv":
            import csv
            # Implement CSV export based on report structure
            logger.info(f"CSV export not fully implemented for {report_type}")
        
        elif format == "excel":
            # Implement Excel export using openpyxl
            logger.info(f"Excel export not fully implemented for {report_type}")
        
        logger.info(f"Report exported: {filepath}")
        
        # Publish event
        event_bus.publish(
            event_type="reports.exported",
            data={
                "report_type": report_type,
                "report_date": report_date,
                "format": format,
                "destination": destination,
                "filepath": str(filepath)
            },
            priority=EventPriority.LOW
        )
        
        return {
            "status": "success",
            "message": f"Report exported to {filepath}",
            "filepath": str(filepath),
            "format": format
        }
        
    except Exception as e:
        logger.error(f"Failed to export report: {e}")
        raise self.retry(exc=e, countdown=120, max_retries=2)
