"""
Authentication API endpoints.
"""
from fastapi import APIRout<PERSON>, Depends, HTTPException, Request, status
from sqlalchemy.orm import Session

from app.core.auth import get_current_user, CurrentUser
from app.core.security import create_access_token, create_refresh_token, verify_token
from app.db.database import get_db
from app.events.base import event_bus
from app.models.event import EventPriority
from app.schemas.base import ResponseModel, TokenResponse
from app.schemas.user import (
    AdminLoginRequest,
    ChangePasswordRequest,
    TenantLoginRequest,
    UserLoginRequest,
)
from app.services.user import admin_service, tenant_service, user_service

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/admin/login", response_model=ResponseModel[TokenResponse])
async def admin_login(
    login_data: AdminLoginRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Admin login endpoint.
    
    Args:
        login_data: Admin login credentials
        request: HTTP request
        db: Database session
        
    Returns:
        ResponseModel[TokenResponse]: Login response with tokens
    """
    try:
        # Authenticate admin
        admin = admin_service.authenticate(
            db, username=login_data.username, password=login_data.password
        )
        
        if not admin:
            # Publish login failed event
            await event_bus.publish(
                event_type="auth.login.failed",
                data={
                    "username": login_data.username,
                    "user_type": "admin",
                    "ip_address": request.client.host,
                    "user_agent": request.headers.get("user-agent"),
                    "reason": "invalid_credentials"
                },
                priority=EventPriority.HIGH
            )
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # Create tokens
        access_token = create_access_token(
            data={"sub": str(admin.id), "type": "admin", "tenant_id": None}
        )
        refresh_token = create_refresh_token(
            data={"sub": str(admin.id), "type": "admin", "tenant_id": None}
        )
        
        # Publish login success event
        await event_bus.publish(
            event_type="auth.login.success",
            data={
                "user_id": admin.id,
                "user_type": "admin",
                "ip_address": request.client.host,
                "user_agent": request.headers.get("user-agent")
            },
            priority=EventPriority.NORMAL
        )
        
        token_response = TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=30 * 60  # 30 minutes
        )
        
        return ResponseModel.success_response(
            data=token_response,
            message="登录成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/tenant/login", response_model=ResponseModel[TokenResponse])
async def tenant_login(
    login_data: TenantLoginRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    Tenant login endpoint.
    
    Args:
        login_data: Tenant login credentials
        request: HTTP request
        db: Database session
        
    Returns:
        ResponseModel[TokenResponse]: Login response with tokens
    """
    try:
        # Authenticate tenant
        tenant = tenant_service.authenticate(
            db, code=login_data.code, password=login_data.password
        )
        
        if not tenant:
            # Publish login failed event
            await event_bus.publish(
                event_type="auth.login.failed",
                data={
                    "username": login_data.code,
                    "user_type": "tenant",
                    "ip_address": request.client.host,
                    "user_agent": request.headers.get("user-agent"),
                    "reason": "invalid_credentials"
                },
                priority=EventPriority.HIGH
            )
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="租户代码或密码错误"
            )
        
        # Create tokens
        access_token = create_access_token(
            data={"sub": str(tenant.id), "type": "tenant", "tenant_id": tenant.id}
        )
        refresh_token = create_refresh_token(
            data={"sub": str(tenant.id), "type": "tenant", "tenant_id": tenant.id}
        )
        
        # Publish login success event
        await event_bus.publish(
            event_type="auth.login.success",
            data={
                "user_id": tenant.id,
                "user_type": "tenant",
                "ip_address": request.client.host,
                "user_agent": request.headers.get("user-agent")
            },
            priority=EventPriority.NORMAL
        )
        
        token_response = TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=30 * 60  # 30 minutes
        )
        
        return ResponseModel.success_response(
            data=token_response,
            message="登录成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/user/login", response_model=ResponseModel[TokenResponse])
async def user_login(
    login_data: UserLoginRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """
    User login endpoint.
    
    Args:
        login_data: User login credentials
        request: HTTP request
        db: Database session
        
    Returns:
        ResponseModel[TokenResponse]: Login response with tokens
    """
    try:
        # Authenticate user
        user = user_service.authenticate(
            db,
            username=login_data.username,
            password=login_data.password,
            tenant_code=login_data.tenant_code
        )
        
        if not user:
            # Publish login failed event
            await event_bus.publish(
                event_type="auth.login.failed",
                data={
                    "username": f"{login_data.tenant_code}:{login_data.username}",
                    "user_type": "user",
                    "ip_address": request.client.host,
                    "user_agent": request.headers.get("user-agent"),
                    "reason": "invalid_credentials"
                },
                priority=EventPriority.HIGH
            )
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名、密码或租户代码错误"
            )
        
        # Create tokens
        access_token = create_access_token(
            data={"sub": str(user.id), "type": "user", "tenant_id": user.tenant_id}
        )
        refresh_token = create_refresh_token(
            data={"sub": str(user.id), "type": "user", "tenant_id": user.tenant_id}
        )
        
        # Publish login success event
        await event_bus.publish(
            event_type="auth.login.success",
            data={
                "user_id": user.id,
                "user_type": "user",
                "ip_address": request.client.host,
                "user_agent": request.headers.get("user-agent")
            },
            priority=EventPriority.NORMAL
        )
        
        token_response = TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=30 * 60  # 30 minutes
        )
        
        return ResponseModel.success_response(
            data=token_response,
            message="登录成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}"
        )


@router.post("/refresh", response_model=ResponseModel[TokenResponse])
async def refresh_token(
    refresh_token: str,
    db: Session = Depends(get_db)
):
    """
    Refresh access token using refresh token.
    
    Args:
        refresh_token: Refresh token
        db: Database session
        
    Returns:
        ResponseModel[TokenResponse]: New tokens
    """
    try:
        # Verify refresh token
        payload = verify_token(refresh_token)
        if not payload:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="无效的刷新令牌"
            )
        
        user_id = payload.get("sub")
        user_type = payload.get("type")
        tenant_id = payload.get("tenant_id")
        
        # Create new tokens
        access_token = create_access_token(
            data={"sub": user_id, "type": user_type, "tenant_id": tenant_id}
        )
        new_refresh_token = create_refresh_token(
            data={"sub": user_id, "type": user_type, "tenant_id": tenant_id}
        )
        
        token_response = TokenResponse(
            access_token=access_token,
            refresh_token=new_refresh_token,
            expires_in=30 * 60  # 30 minutes
        )
        
        return ResponseModel.success_response(
            data=token_response,
            message="令牌刷新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"令牌刷新失败: {str(e)}"
        )


@router.post("/change-password", response_model=ResponseModel[None])
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: CurrentUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Change user password.
    
    Args:
        password_data: Password change data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        ResponseModel[None]: Success response
    """
    try:
        if not password_data.validate_passwords_match():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="新密码和确认密码不匹配"
            )
        
        # Change password based on user type
        if current_user.is_admin:
            admin_service.change_password(
                db,
                admin_id=current_user.id,
                password_data=password_data,
                updated_by=current_user.id
            )
        elif current_user.is_tenant:
            # Implement tenant password change
            pass
        elif current_user.is_user:
            # Implement user password change
            pass
        
        return ResponseModel.success_response(message="密码修改成功")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"密码修改失败: {str(e)}"
        )


@router.get("/me", response_model=ResponseModel[dict])
async def get_current_user_info(
    current_user: CurrentUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        ResponseModel[dict]: User information
    """
    try:
        user_info = {
            "id": current_user.id,
            "type": current_user.user_type,
            "tenant_id": current_user.tenant_id,
            "is_admin": current_user.is_admin,
            "is_tenant": current_user.is_tenant,
            "is_user": current_user.is_user,
        }
        
        # Get detailed user information based on type
        if current_user.is_admin:
            admin = admin_service.get(db, current_user.id)
            user_info.update({
                "username": admin.username,
                "email": admin.email,
                "full_name": admin.full_name,
                "is_superuser": admin.is_superuser,
                "status": admin.status,
                "is_active": admin.is_active,
            })
        elif current_user.is_tenant:
            tenant = tenant_service.get(db, current_user.id)
            user_info.update({
                "name": tenant.name,
                "code": tenant.code,
                "email": tenant.email,
                "description": tenant.description,
                "status": tenant.status,
                "is_active": tenant.is_active,
            })
        elif current_user.is_user:
            user = user_service.get(db, current_user.id)
            user_info.update({
                "username": user.username,
                "email": user.email,
                "full_name": user.full_name,
                "status": user.status,
                "is_active": user.is_active,
            })
        
        return ResponseModel.success_response(
            data=user_info,
            message="获取用户信息成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户信息失败: {str(e)}"
        )
