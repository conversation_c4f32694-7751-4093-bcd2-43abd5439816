"""
API package initialization.
"""
from fastapi import APIRouter

from .admins import router as admins_router
from .auth import router as auth_router
from .tenants import router as tenants_router
from .users import router as users_router

# Create main API router
api_router = APIRouter(prefix="/api/v1")

# Include all routers
api_router.include_router(auth_router)
api_router.include_router(admins_router)
api_router.include_router(tenants_router)
api_router.include_router(users_router)

__all__ = ["api_router"]