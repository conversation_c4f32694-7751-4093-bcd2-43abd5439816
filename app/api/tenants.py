"""
Tenant management API endpoints.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.auth import CurrentUser, get_current_user
from app.core.permissions import require_permission
from app.db.database import get_db
from app.events.base import event_bus
from app.models.event import EventPriority
from app.schemas.base import PaginatedResponse, PaginationParams, ResponseModel
from app.schemas.user import TenantCreate, TenantResponse, TenantUpdate
from app.services.user import tenant_service

router = APIRouter(prefix="/tenants", tags=["Tenant Management"])


@router.post("", response_model=ResponseModel[TenantResponse])
async def create_tenant(
    tenant_data: TenantCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("tenant:create"))
):
    """
    Create a new tenant.
    
    Args:
        tenant_data: Tenant creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[TenantResponse]: Created tenant
    """
    try:
        # Only admins can create tenants
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以创建租户"
            )
        
        # Create tenant
        tenant = tenant_service.create_tenant(
            db, tenant_in=tenant_data, created_by=current_user.id
        )
        
        # Publish tenant created event
        await event_bus.publish(
            event_type="tenant.created",
            data={
                "tenant_id": tenant.id,
                "tenant_code": tenant.code,
                "tenant_name": tenant.name,
                "created_by": current_user.id
            },
            priority=EventPriority.HIGH,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(
            data=TenantResponse.from_orm(tenant),
            message="租户创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"租户创建失败: {str(e)}"
        )


@router.get("", response_model=ResponseModel[PaginatedResponse[TenantResponse]])
async def get_tenants(
    pagination: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("tenant:read"))
):
    """
    Get tenants with pagination.
    
    Args:
        pagination: Pagination parameters
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[PaginatedResponse[TenantResponse]]: Paginated tenants
    """
    try:
        # Only admins can view all tenants, tenants can only view themselves
        if current_user.is_admin:
            tenants = tenant_service.get_multi(
                db, skip=pagination.skip, limit=pagination.limit
            )
            total = tenant_service.count(db)
        elif current_user.is_tenant:
            tenant = tenant_service.get(db, current_user.id)
            tenants = [tenant]
            total = 1
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权查看租户列表"
            )
        
        # Convert to response models
        tenant_responses = [TenantResponse.from_orm(tenant) for tenant in tenants]
        
        # Create paginated response
        paginated_response = PaginatedResponse.create(
            data=tenant_responses,
            page=pagination.page,
            page_size=pagination.page_size,
            total=total
        )
        
        return ResponseModel.success_response(
            data=paginated_response,
            message="获取租户列表成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租户列表失败: {str(e)}"
        )


@router.get("/{tenant_id}", response_model=ResponseModel[TenantResponse])
async def get_tenant(
    tenant_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("tenant:read"))
):
    """
    Get tenant by ID.
    
    Args:
        tenant_id: Tenant ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[TenantResponse]: Tenant information
    """
    try:
        # Check access permissions
        if not current_user.is_admin:
            if current_user.is_tenant and current_user.id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问其他租户信息"
                )
            elif current_user.is_user:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户无权直接访问租户信息"
                )
        
        # Get tenant
        tenant = tenant_service.get(db, tenant_id)
        
        return ResponseModel.success_response(
            data=TenantResponse.from_orm(tenant),
            message="获取租户信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租户信息失败: {str(e)}"
        )


@router.put("/{tenant_id}", response_model=ResponseModel[TenantResponse])
async def update_tenant(
    tenant_id: int,
    tenant_data: TenantUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("tenant:update"))
):
    """
    Update tenant.
    
    Args:
        tenant_id: Tenant ID
        tenant_data: Tenant update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[TenantResponse]: Updated tenant
    """
    try:
        # Check access permissions
        if not current_user.is_admin:
            if current_user.is_tenant and current_user.id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权修改其他租户信息"
                )
            elif current_user.is_user:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户无权修改租户信息"
                )
        
        # Update tenant
        update_data = tenant_data.dict(exclude_unset=True)
        updated_tenant = tenant_service.update_tenant(
            db, tenant_id=tenant_id, tenant_in=tenant_data, updated_by=current_user.id
        )
        
        # Publish tenant updated event
        await event_bus.publish(
            event_type="tenant.updated",
            data={
                "tenant_id": tenant_id,
                "updated_fields": list(update_data.keys()),
                "updated_by": current_user.id
            },
            priority=EventPriority.NORMAL,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(
            data=TenantResponse.from_orm(updated_tenant),
            message="租户更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"租户更新失败: {str(e)}"
        )


@router.delete("/{tenant_id}", response_model=ResponseModel[None])
async def delete_tenant(
    tenant_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("tenant:delete"))
):
    """
    Delete tenant.
    
    Args:
        tenant_id: Tenant ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[None]: Success response
    """
    try:
        # Only admins can delete tenants
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以删除租户"
            )
        
        # Get existing tenant
        existing_tenant = tenant_service.get(db, tenant_id)
        
        # Check if tenant has users
        from app.services.user import user_service
        user_count = user_service.dao.count_by_tenant(db, tenant_id=tenant_id)
        if user_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无法删除租户，该租户下还有 {user_count} 个用户"
            )
        
        # Delete tenant
        tenant_service.delete(db, id=tenant_id)
        
        # Publish tenant deleted event
        await event_bus.publish(
            event_type="tenant.deleted",
            data={
                "tenant_id": tenant_id,
                "tenant_code": existing_tenant.code,
                "tenant_name": existing_tenant.name,
                "deleted_by": current_user.id
            },
            priority=EventPriority.HIGH,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(message="租户删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"租户删除失败: {str(e)}"
        )


@router.get("/{tenant_id}/users", response_model=ResponseModel[PaginatedResponse])
async def get_tenant_users(
    tenant_id: int,
    pagination: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("user:read"))
):
    """
    Get users in a tenant.
    
    Args:
        tenant_id: Tenant ID
        pagination: Pagination parameters
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[PaginatedResponse]: Paginated users in tenant
    """
    try:
        # Check access permissions
        if not current_user.is_admin:
            if current_user.is_tenant and current_user.id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问其他租户的用户"
                )
            elif current_user.is_user and current_user.tenant_id != tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问其他租户的用户"
                )
        
        # Get tenant users
        from app.services.user import user_service
        from app.schemas.user import UserResponse
        
        users = user_service.get_users_by_tenant(
            db, tenant_id=tenant_id, skip=pagination.skip, limit=pagination.limit
        )
        total = user_service.dao.count_by_tenant(db, tenant_id=tenant_id)
        
        # Convert to response models
        user_responses = [UserResponse.from_orm(user) for user in users]
        
        # Create paginated response
        paginated_response = PaginatedResponse.create(
            data=user_responses,
            page=pagination.page,
            page_size=pagination.page_size,
            total=total
        )
        
        return ResponseModel.success_response(
            data=paginated_response,
            message="获取租户用户列表成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租户用户列表失败: {str(e)}"
        )
