"""
User management API endpoints.
"""
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.auth import CurrentUser, get_current_user
from app.core.permissions import require_permission
from app.db.database import get_db
from app.events.base import event_bus
from app.models.event import EventPriority
from app.schemas.base import PaginatedResponse, PaginationParams, ResponseModel
from app.schemas.user import UserCreate, UserResponse, UserUpdate, UserWithTenant
from app.services.user import user_service

router = APIRouter(prefix="/users", tags=["User Management"])


@router.post("", response_model=ResponseModel[UserResponse])
async def create_user(
    user_data: UserCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("user:create"))
):
    """
    Create a new user.
    
    Args:
        user_data: User creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[UserResponse]: Created user
    """
    try:
        # Check tenant access for non-admin users
        if not current_user.is_admin:
            if current_user.is_tenant and current_user.id != user_data.tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权在其他租户中创建用户"
                )
            elif current_user.is_user:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户无权创建其他用户"
                )
        
        # Create user
        user = user_service.create_user(
            db, user_in=user_data, created_by=current_user.id
        )
        
        # Publish user created event
        await event_bus.publish(
            event_type="user.created",
            data={
                "user_id": user.id,
                "tenant_id": user.tenant_id,
                "username": user.username,
                "email": user.email,
                "created_by": current_user.id
            },
            priority=EventPriority.NORMAL,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(
            data=UserResponse.from_orm(user),
            message="用户创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"用户创建失败: {str(e)}"
        )


@router.get("", response_model=ResponseModel[PaginatedResponse[UserResponse]])
async def get_users(
    pagination: PaginationParams = Depends(),
    tenant_id: int = None,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("user:read"))
):
    """
    Get users with pagination.
    
    Args:
        pagination: Pagination parameters
        tenant_id: Filter by tenant ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[PaginatedResponse[UserResponse]]: Paginated users
    """
    try:
        # Apply tenant filtering for non-admin users
        if not current_user.is_admin:
            if current_user.is_tenant:
                tenant_id = current_user.id
            elif current_user.is_user:
                tenant_id = current_user.tenant_id
        
        # Get users
        if tenant_id:
            users = user_service.get_users_by_tenant(
                db, tenant_id=tenant_id, skip=pagination.skip, limit=pagination.limit
            )
            total = user_service.dao.count_by_tenant(db, tenant_id=tenant_id)
        else:
            users = user_service.get_multi(
                db, skip=pagination.skip, limit=pagination.limit
            )
            total = user_service.count(db)
        
        # Convert to response models
        user_responses = [UserResponse.from_orm(user) for user in users]
        
        # Create paginated response
        paginated_response = PaginatedResponse.create(
            data=user_responses,
            page=pagination.page,
            page_size=pagination.page_size,
            total=total
        )
        
        return ResponseModel.success_response(
            data=paginated_response,
            message="获取用户列表成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户列表失败: {str(e)}"
        )


@router.get("/{user_id}", response_model=ResponseModel[UserWithTenant])
async def get_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("user:read"))
):
    """
    Get user by ID.
    
    Args:
        user_id: User ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[UserWithTenant]: User with tenant information
    """
    try:
        # Get user
        user = user_service.get(db, user_id)
        
        # Check access permissions
        if not current_user.is_admin:
            if current_user.is_tenant and current_user.id != user.tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问其他租户的用户"
                )
            elif current_user.is_user and current_user.id != user_id:
                if current_user.tenant_id != user.tenant_id:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="无权访问其他租户的用户"
                    )
        
        # Get tenant information
        from app.services.user import tenant_service
        tenant = tenant_service.get(db, user.tenant_id)
        
        # Create response with tenant info
        user_response = UserWithTenant.from_orm(user)
        user_response.tenant = tenant
        
        return ResponseModel.success_response(
            data=user_response,
            message="获取用户信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户信息失败: {str(e)}"
        )


@router.put("/{user_id}", response_model=ResponseModel[UserResponse])
async def update_user(
    user_id: int,
    user_data: UserUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("user:update"))
):
    """
    Update user.
    
    Args:
        user_id: User ID
        user_data: User update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[UserResponse]: Updated user
    """
    try:
        # Get existing user
        existing_user = user_service.get(db, user_id)
        
        # Check access permissions
        if not current_user.is_admin:
            if current_user.is_tenant and current_user.id != existing_user.tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权修改其他租户的用户"
                )
            elif current_user.is_user and current_user.id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权修改其他用户"
                )
        
        # Update user
        update_data = user_data.dict(exclude_unset=True)
        updated_user = user_service.update(
            db, id=user_id, obj_in=update_data, updated_by=current_user.id
        )
        
        # Publish user updated event
        await event_bus.publish(
            event_type="user.updated",
            data={
                "user_id": user_id,
                "tenant_id": updated_user.tenant_id,
                "updated_fields": list(update_data.keys()),
                "updated_by": current_user.id
            },
            priority=EventPriority.NORMAL,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(
            data=UserResponse.from_orm(updated_user),
            message="用户更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"用户更新失败: {str(e)}"
        )


@router.delete("/{user_id}", response_model=ResponseModel[None])
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("user:delete"))
):
    """
    Delete user.
    
    Args:
        user_id: User ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[None]: Success response
    """
    try:
        # Get existing user
        existing_user = user_service.get(db, user_id)
        
        # Check access permissions
        if not current_user.is_admin:
            if current_user.is_tenant and current_user.id != existing_user.tenant_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权删除其他租户的用户"
                )
            elif current_user.is_user:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="用户无权删除其他用户"
                )
        
        # Delete user
        user_service.delete(db, id=user_id)
        
        # Publish user deleted event
        await event_bus.publish(
            event_type="user.deleted",
            data={
                "user_id": user_id,
                "tenant_id": existing_user.tenant_id,
                "username": existing_user.username,
                "deleted_by": current_user.id
            },
            priority=EventPriority.HIGH,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(message="用户删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"用户删除失败: {str(e)}"
        )


@router.get("/search", response_model=ResponseModel[List[UserResponse]])
async def search_users(
    query: str,
    tenant_id: int = None,
    limit: int = 20,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("user:read"))
):
    """
    Search users.
    
    Args:
        query: Search query
        tenant_id: Filter by tenant ID
        limit: Maximum number of results
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[List[UserResponse]]: Search results
    """
    try:
        # Apply tenant filtering for non-admin users
        if not current_user.is_admin:
            if current_user.is_tenant:
                tenant_id = current_user.id
            elif current_user.is_user:
                tenant_id = current_user.tenant_id
        
        # Search users
        users = user_service.search_users(
            db, query=query, tenant_id=tenant_id, limit=limit
        )
        
        # Convert to response models
        user_responses = [UserResponse.from_orm(user) for user in users]
        
        return ResponseModel.success_response(
            data=user_responses,
            message="用户搜索成功"
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"用户搜索失败: {str(e)}"
        )
