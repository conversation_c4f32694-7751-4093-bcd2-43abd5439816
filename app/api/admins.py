"""
Admin management API endpoints.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.core.auth import CurrentUser, get_current_user
from app.core.permissions import require_permission
from app.db.database import get_db
from app.events.base import event_bus
from app.models.event import EventPriority
from app.schemas.base import PaginatedResponse, PaginationParams, ResponseModel
from app.schemas.user import AdminCreate, AdminResponse, AdminUpdate
from app.services.user import admin_service

router = APIRouter(prefix="/admins", tags=["Admin Management"])


@router.post("", response_model=ResponseModel[AdminResponse])
async def create_admin(
    admin_data: AdminCreate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("admin:create"))
):
    """
    Create a new admin.
    
    Args:
        admin_data: Admin creation data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[AdminResponse]: Created admin
    """
    try:
        # Only superuser admins can create other admins
        current_admin = admin_service.get(db, current_user.id)
        if not current_admin.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有超级管理员可以创建其他管理员"
            )
        
        # Create admin
        admin = admin_service.create_admin(
            db, admin_in=admin_data, created_by=current_user.id
        )
        
        # Publish admin created event
        await event_bus.publish(
            event_type="admin.created",
            data={
                "admin_id": admin.id,
                "username": admin.username,
                "email": admin.email,
                "is_superuser": admin.is_superuser,
                "created_by": current_user.id
            },
            priority=EventPriority.HIGH,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(
            data=AdminResponse.from_orm(admin),
            message="管理员创建成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"管理员创建失败: {str(e)}"
        )


@router.get("", response_model=ResponseModel[PaginatedResponse[AdminResponse]])
async def get_admins(
    pagination: PaginationParams = Depends(),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("admin:read"))
):
    """
    Get admins with pagination.
    
    Args:
        pagination: Pagination parameters
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[PaginatedResponse[AdminResponse]]: Paginated admins
    """
    try:
        # Only admins can view admin list
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以查看管理员列表"
            )
        
        # Get admins
        admins = admin_service.get_multi(
            db, skip=pagination.skip, limit=pagination.limit
        )
        total = admin_service.count(db)
        
        # Convert to response models
        admin_responses = [AdminResponse.from_orm(admin) for admin in admins]
        
        # Create paginated response
        paginated_response = PaginatedResponse.create(
            data=admin_responses,
            page=pagination.page,
            page_size=pagination.page_size,
            total=total
        )
        
        return ResponseModel.success_response(
            data=paginated_response,
            message="获取管理员列表成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取管理员列表失败: {str(e)}"
        )


@router.get("/{admin_id}", response_model=ResponseModel[AdminResponse])
async def get_admin(
    admin_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("admin:read"))
):
    """
    Get admin by ID.
    
    Args:
        admin_id: Admin ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[AdminResponse]: Admin information
    """
    try:
        # Only admins can view admin details, and only themselves or superusers can view others
        if not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以查看管理员信息"
            )
        
        if current_user.id != admin_id:
            current_admin = admin_service.get(db, current_user.id)
            if not current_admin.is_superuser:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有超级管理员可以查看其他管理员信息"
                )
        
        # Get admin
        admin = admin_service.get(db, admin_id)
        
        return ResponseModel.success_response(
            data=AdminResponse.from_orm(admin),
            message="获取管理员信息成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取管理员信息失败: {str(e)}"
        )


@router.put("/{admin_id}", response_model=ResponseModel[AdminResponse])
async def update_admin(
    admin_id: int,
    admin_data: AdminUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("admin:update"))
):
    """
    Update admin.
    
    Args:
        admin_id: Admin ID
        admin_data: Admin update data
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[AdminResponse]: Updated admin
    """
    try:
        # Check permissions
        if current_user.id != admin_id:
            current_admin = admin_service.get(db, current_user.id)
            if not current_admin.is_superuser:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有超级管理员可以修改其他管理员信息"
                )
        
        # Get existing admin
        existing_admin = admin_service.get(db, admin_id)
        
        # Prevent non-superuser from modifying superuser status
        update_data = admin_data.dict(exclude_unset=True)
        if "is_superuser" in update_data:
            current_admin = admin_service.get(db, current_user.id)
            if not current_admin.is_superuser:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有超级管理员可以修改超级管理员状态"
                )
        
        # Update admin
        updated_admin = admin_service.update_admin(
            db, admin_id=admin_id, admin_in=admin_data, updated_by=current_user.id
        )
        
        # Publish admin updated event
        await event_bus.publish(
            event_type="admin.updated",
            data={
                "admin_id": admin_id,
                "updated_fields": list(update_data.keys()),
                "updated_by": current_user.id
            },
            priority=EventPriority.NORMAL,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(
            data=AdminResponse.from_orm(updated_admin),
            message="管理员更新成功"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"管理员更新失败: {str(e)}"
        )


@router.delete("/{admin_id}", response_model=ResponseModel[None])
async def delete_admin(
    admin_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("admin:delete"))
):
    """
    Delete admin.
    
    Args:
        admin_id: Admin ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[None]: Success response
    """
    try:
        # Only superuser admins can delete other admins
        current_admin = admin_service.get(db, current_user.id)
        if not current_admin.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有超级管理员可以删除其他管理员"
            )
        
        # Prevent self-deletion
        if current_user.id == admin_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能删除自己的管理员账户"
            )
        
        # Get existing admin
        existing_admin = admin_service.get(db, admin_id)
        
        # Check if it's the last superuser
        if existing_admin.is_superuser:
            superuser_count = admin_service.dao.count_superusers(db)
            if superuser_count <= 1:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能删除最后一个超级管理员"
                )
        
        # Delete admin
        admin_service.delete(db, id=admin_id)
        
        # Publish admin deleted event
        await event_bus.publish(
            event_type="admin.deleted",
            data={
                "admin_id": admin_id,
                "username": existing_admin.username,
                "email": existing_admin.email,
                "was_superuser": existing_admin.is_superuser,
                "deleted_by": current_user.id
            },
            priority=EventPriority.HIGH,
            created_by=current_user.id
        )
        
        return ResponseModel.success_response(message="管理员删除成功")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"管理员删除失败: {str(e)}"
        )


@router.post("/{admin_id}/toggle-superuser", response_model=ResponseModel[AdminResponse])
async def toggle_superuser_status(
    admin_id: int,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission("admin:update"))
):
    """
    Toggle admin superuser status.
    
    Args:
        admin_id: Admin ID
        db: Database session
        current_user: Current authenticated user
        
    Returns:
        ResponseModel[AdminResponse]: Updated admin
    """
    try:
        # Only superuser admins can toggle superuser status
        current_admin = admin_service.get(db, current_user.id)
        if not current_admin.is_superuser:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有超级管理员可以修改超级管理员状态"
            )
        
        # Get target admin
        target_admin = admin_service.get(db, admin_id)
        
        # Prevent removing superuser status from the last superuser
        if target_admin.is_superuser:
            superuser_count = admin_service.dao.count_superusers(db)
            if superuser_count <= 1:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能移除最后一个超级管理员的超级管理员状态"
                )
        
        # Toggle superuser status
        new_status = not target_admin.is_superuser
        updated_admin = admin_service.update(
            db,
            id=admin_id,
            obj_in={"is_superuser": new_status},
            updated_by=current_user.id
        )
        
        # Publish admin updated event
        await event_bus.publish(
            event_type="admin.superuser_toggled",
            data={
                "admin_id": admin_id,
                "new_superuser_status": new_status,
                "toggled_by": current_user.id
            },
            priority=EventPriority.HIGH,
            created_by=current_user.id
        )
        
        action = "授予" if new_status else "移除"
        return ResponseModel.success_response(
            data=AdminResponse.from_orm(updated_admin),
            message=f"成功{action}超级管理员权限"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"修改超级管理员状态失败: {str(e)}"
        )
