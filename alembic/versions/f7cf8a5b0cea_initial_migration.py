"""Initial migration

Revision ID: f7cf8a5b0cea
Revises: 
Create Date: 2025-07-27 23:28:31.721628

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'f7cf8a5b0cea'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('admins',
    sa.<PERSON>umn('username', sa.String(length=50), nullable=False, comment='管理员用户名'),
    sa.Column('email', sa.String(length=100), nullable=False, comment='邮箱地址'),
    sa.Column('hashed_password', sa.String(length=255), nullable=False, comment='加密密码'),
    sa.Column('full_name', sa.String(length=100), nullable=True, comment='全名'),
    sa.Column('is_active', sa.<PERSON>an(), nullable=False, comment='是否激活'),
    sa.Column('is_superuser', sa.<PERSON>(), nullable=False, comment='是否超级管理员'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED', name='userstatus'), nullable=False, comment='用户状态'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_admins_email'), 'admins', ['email'], unique=True)
    op.create_index(op.f('ix_admins_id'), 'admins', ['id'], unique=False)
    op.create_index(op.f('ix_admins_username'), 'admins', ['username'], unique=True)
    op.create_table('event_logs',
    sa.Column('event_id', sa.Integer(), nullable=False, comment='事件ID'),
    sa.Column('handler_name', sa.String(length=100), nullable=False, comment='处理器名称'),
    sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', name='eventstatus'), nullable=False, comment='处理状态'),
    sa.Column('execution_time', sa.Float(), nullable=True, comment='执行时间(秒)'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('result', sa.JSON(), nullable=True, comment='处理结果'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_event_logs_event_id'), 'event_logs', ['event_id'], unique=False)
    op.create_index(op.f('ix_event_logs_id'), 'event_logs', ['id'], unique=False)
    op.create_table('event_subscriptions',
    sa.Column('event_type', sa.String(length=50), nullable=False, comment='订阅的事件类型'),
    sa.Column('handler_name', sa.String(length=100), nullable=False, comment='处理器名称'),
    sa.Column('handler_module', sa.String(length=200), nullable=False, comment='处理器模块路径'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('priority', sa.Integer(), nullable=False, comment='处理优先级'),
    sa.Column('description', sa.Text(), nullable=True, comment='订阅描述'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_event_subscriptions_event_type'), 'event_subscriptions', ['event_type'], unique=False)
    op.create_index(op.f('ix_event_subscriptions_id'), 'event_subscriptions', ['id'], unique=False)
    op.create_table('events',
    sa.Column('name', sa.String(length=100), nullable=False, comment='事件名称'),
    sa.Column('event_type', sa.String(length=50), nullable=False, comment='事件类型'),
    sa.Column('source', sa.String(length=100), nullable=False, comment='事件源'),
    sa.Column('payload', sa.JSON(), nullable=True, comment='事件数据'),
    sa.Column('status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', name='eventstatus'), nullable=False, comment='事件状态'),
    sa.Column('priority', sa.Enum('LOW', 'NORMAL', 'HIGH', 'CRITICAL', name='eventpriority'), nullable=False, comment='事件优先级'),
    sa.Column('error_message', sa.Text(), nullable=True, comment='错误信息'),
    sa.Column('retry_count', sa.Integer(), nullable=False, comment='重试次数'),
    sa.Column('max_retries', sa.Integer(), nullable=False, comment='最大重试次数'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_events_event_type'), 'events', ['event_type'], unique=False)
    op.create_index(op.f('ix_events_id'), 'events', ['id'], unique=False)
    op.create_index(op.f('ix_events_name'), 'events', ['name'], unique=False)
    op.create_index(op.f('ix_events_status'), 'events', ['status'], unique=False)
    op.create_table('permissions',
    sa.Column('name', sa.String(length=100), nullable=False, comment='权限名称'),
    sa.Column('code', sa.String(length=50), nullable=False, comment='权限代码'),
    sa.Column('description', sa.Text(), nullable=True, comment='权限描述'),
    sa.Column('resource_type', sa.Enum('USER', 'TENANT', 'ADMIN', 'PERMISSION', 'ROLE', 'SYSTEM', name='resourcetype'), nullable=False, comment='资源类型'),
    sa.Column('permission_type', sa.Enum('CREATE', 'READ', 'UPDATE', 'DELETE', 'MANAGE', 'EXECUTE', name='permissiontype'), nullable=False, comment='权限类型'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_permissions_code'), 'permissions', ['code'], unique=True)
    op.create_index(op.f('ix_permissions_id'), 'permissions', ['id'], unique=False)
    op.create_index(op.f('ix_permissions_name'), 'permissions', ['name'], unique=True)
    op.create_table('roles',
    sa.Column('name', sa.String(length=100), nullable=False, comment='角色名称'),
    sa.Column('code', sa.String(length=50), nullable=False, comment='角色代码'),
    sa.Column('description', sa.Text(), nullable=True, comment='角色描述'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_roles_code'), 'roles', ['code'], unique=True)
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=True)
    op.create_table('tenants',
    sa.Column('name', sa.String(length=100), nullable=False, comment='租户名称'),
    sa.Column('code', sa.String(length=50), nullable=False, comment='租户代码'),
    sa.Column('email', sa.String(length=100), nullable=False, comment='租户邮箱'),
    sa.Column('hashed_password', sa.String(length=255), nullable=False, comment='加密密码'),
    sa.Column('description', sa.Text(), nullable=True, comment='租户描述'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED', name='userstatus'), nullable=False, comment='租户状态'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_tenants_code'), 'tenants', ['code'], unique=True)
    op.create_index(op.f('ix_tenants_email'), 'tenants', ['email'], unique=True)
    op.create_index(op.f('ix_tenants_id'), 'tenants', ['id'], unique=False)
    op.create_index(op.f('ix_tenants_name'), 'tenants', ['name'], unique=True)
    op.create_table('admin_permissions',
    sa.Column('admin_id', sa.Integer(), nullable=False, comment='管理员ID'),
    sa.Column('permission_id', sa.Integer(), nullable=False, comment='权限ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.ForeignKeyConstraint(['admin_id'], ['admins.id'], ),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('admin_id', 'permission_id', name='uq_admin_permission')
    )
    op.create_index(op.f('ix_admin_permissions_id'), 'admin_permissions', ['id'], unique=False)
    op.create_table('admin_roles',
    sa.Column('admin_id', sa.Integer(), nullable=False, comment='管理员ID'),
    sa.Column('role_id', sa.Integer(), nullable=False, comment='角色ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.ForeignKeyConstraint(['admin_id'], ['admins.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('admin_id', 'role_id', name='uq_admin_role')
    )
    op.create_index(op.f('ix_admin_roles_id'), 'admin_roles', ['id'], unique=False)
    op.create_table('role_permissions',
    sa.Column('role_id', sa.Integer(), nullable=False, comment='角色ID'),
    sa.Column('permission_id', sa.Integer(), nullable=False, comment='权限ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('role_id', 'permission_id', name='uq_role_permission')
    )
    op.create_index(op.f('ix_role_permissions_id'), 'role_permissions', ['id'], unique=False)
    op.create_table('tenant_permissions',
    sa.Column('tenant_id', sa.Integer(), nullable=False, comment='租户ID'),
    sa.Column('permission_id', sa.Integer(), nullable=False, comment='权限ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'permission_id', name='uq_tenant_permission')
    )
    op.create_index(op.f('ix_tenant_permissions_id'), 'tenant_permissions', ['id'], unique=False)
    op.create_table('tenant_roles',
    sa.Column('tenant_id', sa.Integer(), nullable=False, comment='租户ID'),
    sa.Column('role_id', sa.Integer(), nullable=False, comment='角色ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'role_id', name='uq_tenant_role')
    )
    op.create_index(op.f('ix_tenant_roles_id'), 'tenant_roles', ['id'], unique=False)
    op.create_table('users',
    sa.Column('username', sa.String(length=50), nullable=False, comment='用户名'),
    sa.Column('email', sa.String(length=100), nullable=False, comment='邮箱地址'),
    sa.Column('hashed_password', sa.String(length=255), nullable=False, comment='加密密码'),
    sa.Column('full_name', sa.String(length=100), nullable=True, comment='全名'),
    sa.Column('is_active', sa.Boolean(), nullable=False, comment='是否激活'),
    sa.Column('status', sa.Enum('ACTIVE', 'INACTIVE', 'SUSPENDED', 'DELETED', name='userstatus'), nullable=False, comment='用户状态'),
    sa.Column('tenant_id', sa.Integer(), nullable=False, comment='所属租户ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=False)
    op.create_index(op.f('ix_users_id'), 'users', ['id'], unique=False)
    op.create_index(op.f('ix_users_tenant_id'), 'users', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=False)
    op.create_table('user_permissions',
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('permission_id', sa.Integer(), nullable=False, comment='权限ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'permission_id', name='uq_user_permission')
    )
    op.create_index(op.f('ix_user_permissions_id'), 'user_permissions', ['id'], unique=False)
    op.create_table('user_roles',
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('role_id', sa.Integer(), nullable=False, comment='角色ID'),
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), server_default=sa.text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'), nullable=False, comment='更新时间'),
    sa.Column('created_by', sa.Integer(), nullable=True, comment='创建人ID'),
    sa.Column('updated_by', sa.Integer(), nullable=True, comment='更新人ID'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_id', 'role_id', name='uq_user_role')
    )
    op.create_index(op.f('ix_user_roles_id'), 'user_roles', ['id'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_user_roles_id'), table_name='user_roles')
    op.drop_table('user_roles')
    op.drop_index(op.f('ix_user_permissions_id'), table_name='user_permissions')
    op.drop_table('user_permissions')
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_tenant_id'), table_name='users')
    op.drop_index(op.f('ix_users_id'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    op.drop_index(op.f('ix_tenant_roles_id'), table_name='tenant_roles')
    op.drop_table('tenant_roles')
    op.drop_index(op.f('ix_tenant_permissions_id'), table_name='tenant_permissions')
    op.drop_table('tenant_permissions')
    op.drop_index(op.f('ix_role_permissions_id'), table_name='role_permissions')
    op.drop_table('role_permissions')
    op.drop_index(op.f('ix_admin_roles_id'), table_name='admin_roles')
    op.drop_table('admin_roles')
    op.drop_index(op.f('ix_admin_permissions_id'), table_name='admin_permissions')
    op.drop_table('admin_permissions')
    op.drop_index(op.f('ix_tenants_name'), table_name='tenants')
    op.drop_index(op.f('ix_tenants_id'), table_name='tenants')
    op.drop_index(op.f('ix_tenants_email'), table_name='tenants')
    op.drop_index(op.f('ix_tenants_code'), table_name='tenants')
    op.drop_table('tenants')
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.drop_index(op.f('ix_roles_code'), table_name='roles')
    op.drop_table('roles')
    op.drop_index(op.f('ix_permissions_name'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_id'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_code'), table_name='permissions')
    op.drop_table('permissions')
    op.drop_index(op.f('ix_events_status'), table_name='events')
    op.drop_index(op.f('ix_events_name'), table_name='events')
    op.drop_index(op.f('ix_events_id'), table_name='events')
    op.drop_index(op.f('ix_events_event_type'), table_name='events')
    op.drop_table('events')
    op.drop_index(op.f('ix_event_subscriptions_id'), table_name='event_subscriptions')
    op.drop_index(op.f('ix_event_subscriptions_event_type'), table_name='event_subscriptions')
    op.drop_table('event_subscriptions')
    op.drop_index(op.f('ix_event_logs_id'), table_name='event_logs')
    op.drop_index(op.f('ix_event_logs_event_id'), table_name='event_logs')
    op.drop_table('event_logs')
    op.drop_index(op.f('ix_admins_username'), table_name='admins')
    op.drop_index(op.f('ix_admins_id'), table_name='admins')
    op.drop_index(op.f('ix_admins_email'), table_name='admins')
    op.drop_table('admins')
    # ### end Alembic commands ###
