---
type: "always_apply"
---

- 使用 python 3.11.13 
- 使用 fastapi
- 使用 sqlalchemy 作为orm
- 使用 alembic 作为sqlalchemy的迁移工具
- 使用 mysql 5.7作为数据库
- 使用 redis作为缓存和键值数据库
- 使用 celery作为任务管理
- 使用 poetry作为依赖以及虚拟环境管理
- 使用 arrow库进行时间处理
- 使用 requests发起http请求
- 使用 openxsl 处理excel文件 
- 使用 uvicorn 来运行应用

项目结构如下：
alembic （alembic所需的文件目录）
app （应用目录）
docs （应用文档目录）
logs（日志目录）
statics （静态文件目录）
scripts （脚本目录）
tests （测试文件目录）

app目录结构如下：
core 核心代码		
events 事件机制实现		
messager 消息通知
services 服务
api	 api目录	
dao	 dao目录	
handlers 自定义事件处理器目录	
models 模型目录	
tasks 自定义的任务目录
callback 应用回调代码目录
db 数据库相关的实现目录
main.py 应用入口
schemas schema目录		
utils 工具类或函数目录

agent返回的命令行执行的脚本需要同时激活poetry的虚拟环境

  

