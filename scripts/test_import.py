#!/usr/bin/env python3
"""
Test script to verify all imports work correctly.
"""
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test all critical imports."""
    print("Testing imports...")
    
    try:
        # Test config
        from app.core.config import settings
        print("✓ Config imported successfully")
        print(f"  App name: {settings.app_name}")
        print(f"  Environment: {settings.environment}")
        
        # Test database
        from app.db.database import engine, SessionLocal
        print("✓ Database imports successful")
        
        # Test models
        from app.models.user import Admin, Tenant, User
        from app.models.permission import Permission, Role
        from app.models.event import Event, EventLog
        print("✓ Models imported successfully")
        
        # Test services
        from app.services.user import admin_service, tenant_service, user_service
        print("✓ Services imported successfully")
        
        # Test API
        from app.api.auth import router as auth_router
        from app.api.users import router as users_router
        print("✓ API routers imported successfully")
        
        # Test main app
        from app.main import app
        print("✓ FastAPI app imported successfully")
        
        print("\n🎉 All imports successful!")
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config():
    """Test configuration values."""
    print("\nTesting configuration...")
    
    try:
        from app.core.config import settings
        
        print(f"Database URL: {settings.database_url}")
        print(f"Redis URL: {settings.redis_url}")
        print(f"Secret Key: {settings.secret_key[:10]}...")
        print(f"Debug Mode: {settings.debug}")
        
        return True
        
    except Exception as e:
        print(f"✗ Config test failed: {e}")
        return False

if __name__ == "__main__":
    print("PXBLangs Import Test")
    print("=" * 30)
    
    success = True
    
    if not test_imports():
        success = False
    
    if not test_config():
        success = False
    
    if success:
        print("\n✅ All tests passed! The application should start correctly.")
    else:
        print("\n❌ Some tests failed. Please fix the issues before starting the application.")
        sys.exit(1)
