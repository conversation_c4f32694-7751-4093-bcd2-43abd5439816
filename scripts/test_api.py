#!/usr/bin/env python3
"""
Test script to verify API functionality.
"""
import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_health_check():
    """Test health check endpoint."""
    print("Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print("✓ Health check successful")
            print(f"  Status: {data.get('status')}")
            print(f"  App: {data.get('app_name')} v{data.get('version')}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Health check error: {e}")
        return False

def test_api_docs():
    """Test API documentation endpoint."""
    print("\nTesting API documentation...")
    try:
        response = requests.get(f"{BASE_URL}/docs")
        if response.status_code == 200:
            print("✓ API documentation accessible")
            return True
        else:
            print(f"✗ API docs failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ API docs error: {e}")
        return False

def test_openapi_spec():
    """Test OpenAPI specification endpoint."""
    print("\nTesting OpenAPI specification...")
    try:
        response = requests.get(f"{BASE_URL}/openapi.json")
        if response.status_code == 200:
            spec = response.json()
            print("✓ OpenAPI specification accessible")
            print(f"  Title: {spec.get('info', {}).get('title')}")
            print(f"  Version: {spec.get('info', {}).get('version')}")
            print(f"  Paths: {len(spec.get('paths', {}))}")
            return True
        else:
            print(f"✗ OpenAPI spec failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ OpenAPI spec error: {e}")
        return False

def test_auth_endpoints():
    """Test authentication endpoints."""
    print("\nTesting authentication endpoints...")
    try:
        # Test login endpoint (should fail without credentials)
        response = requests.post(f"{BASE_URL}/api/v1/auth/login", json={
            "username": "test",
            "password": "test"
        })
        
        # We expect this to fail since we don't have a user
        if response.status_code in [401, 422, 404]:
            print("✓ Login endpoint responding correctly")
            return True
        else:
            print(f"✗ Login endpoint unexpected response: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Auth endpoints error: {e}")
        return False

def main():
    """Run all tests."""
    print("PXBLangs API Test Suite")
    print("=" * 30)
    
    tests = [
        test_health_check,
        test_api_docs,
        test_openapi_spec,
        test_auth_endpoints
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print(f"\nTest Results: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! The API is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the application.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
