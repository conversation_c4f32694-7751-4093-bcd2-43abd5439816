#!/usr/bin/env python3
"""
Simple script to run the FastAPI application directly.
"""
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def main():
    """Run the FastAPI application."""
    print("Starting PXBLangs FastAPI Application")
    print("=" * 40)
    
    try:
        # Import and configure logging first
        from app.core.logging import setup_logging
        setup_logging()
        print("✓ Logging configured")
        
        # Import the FastAPI app
        from app.main import app
        print("✓ FastAPI app loaded")
        
        # Import uvicorn
        import uvicorn
        
        print("\nStarting server...")
        print("API Documentation: http://localhost:8000/docs")
        print("Health Check: http://localhost:8000/health")
        print("Press Ctrl+C to stop")
        print("-" * 40)
        
        # Run the application
        uvicorn.run(
            "app.main:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n\nApplication stopped by user")
    except Exception as e:
        print(f"\n❌ Failed to start application: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
