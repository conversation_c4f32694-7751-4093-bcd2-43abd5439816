#!/usr/bin/env python3
"""
Application startup script.
"""
import asyncio
import logging
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import settings
from app.core.logging import setup_logging


async def check_dependencies():
    """Check if all dependencies are available."""
    print("Checking dependencies...")
    
    # Check database connection
    try:
        from app.db.database import engine
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        print("✓ Database connection successful")
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False
    
    # Check Redis connection
    try:
        from app.db.redis_client import redis_client
        await redis_client.ping()
        print("✓ Redis connection successful")
    except Exception as e:
        print(f"✗ Redis connection failed: {e}")
        return False
    
    return True


async def initialize_data():
    """Initialize default data."""
    print("Initializing default data...")
    
    try:
        from app.db.database import SessionLocal
        from app.services.user import admin_service
        from app.schemas.user import AdminCreate
        
        db = SessionLocal()
        
        # Check if any admin exists
        existing_admin = admin_service.dao.get_first(db)
        
        if not existing_admin:
            # Create default superuser admin
            default_admin = AdminCreate(
                username="admin",
                email="<EMAIL>",
                password="admin123",  # Change this in production!
                full_name="System Administrator",
                is_superuser=True
            )
            
            admin = admin_service.create(db, obj_in=default_admin)
            print(f"✓ Created default admin: {admin.username}")
        else:
            print("✓ Admin user already exists")
        
        db.close()
        
    except Exception as e:
        print(f"✗ Failed to initialize data: {e}")
        return False
    
    return True


def run_migrations():
    """Run database migrations."""
    print("Running database migrations...")
    
    try:
        import subprocess
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            cwd=project_root,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✓ Database migrations completed")
            return True
        else:
            print(f"✗ Migration failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Failed to run migrations: {e}")
        return False


def start_celery_worker():
    """Start Celery worker in background."""
    print("Starting Celery worker...")
    
    try:
        import subprocess
        
        # Start Celery worker
        worker_cmd = [
            "celery", "-A", "app.core.celery_app", "worker",
            "--loglevel=info",
            "--concurrency=2",
            "--queues=default,email,cleanup,reports,users"
        ]
        
        subprocess.Popen(
            worker_cmd,
            cwd=project_root,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        print("✓ Celery worker started")
        return True
        
    except Exception as e:
        print(f"✗ Failed to start Celery worker: {e}")
        return False


def start_celery_beat():
    """Start Celery beat scheduler in background."""
    print("Starting Celery beat scheduler...")
    
    try:
        import subprocess
        
        # Start Celery beat
        beat_cmd = [
            "celery", "-A", "app.core.celery_app", "beat",
            "--loglevel=info"
        ]
        
        subprocess.Popen(
            beat_cmd,
            cwd=project_root,
            stdout=subprocess.DEVNULL,
            stderr=subprocess.DEVNULL
        )
        
        print("✓ Celery beat scheduler started")
        return True
        
    except Exception as e:
        print(f"✗ Failed to start Celery beat: {e}")
        return False


async def main():
    """Main startup function."""
    print(f"Starting {settings.app_name} v{settings.app_version}")
    print(f"Environment: {settings.environment}")
    print("-" * 50)
    
    # Setup logging
    setup_logging()
    
    # Check environment file
    env_file = project_root / ".env"
    if not env_file.exists():
        print("⚠ Warning: .env file not found. Using default settings.")
        print("Please copy .env.example to .env and configure your settings.")
    
    # Run migrations
    if not run_migrations():
        print("Failed to run migrations. Exiting.")
        sys.exit(1)
    
    # Check dependencies
    if not await check_dependencies():
        print("\nDependency check failed. Please ensure the following services are running:")
        print("- MySQL: brew services start mysql (macOS) or systemctl start mysql (Linux)")
        print("- Redis: brew services start redis (macOS) or systemctl start redis (Linux)")
        print("\nOr run the development setup script: python scripts/dev.py")
        sys.exit(1)
    
    # Initialize default data
    if not await initialize_data():
        print("Failed to initialize data. Exiting.")
        sys.exit(1)
    
    # Start background services
    if settings.environment != "test":
        start_celery_worker()
        start_celery_beat()
    
    print("-" * 50)
    print("✓ All checks passed. Starting application...")
    print(f"API Documentation: http://localhost:8000/docs")
    print(f"Health Check: http://localhost:8000/health")
    print("-" * 50)
    
    # Start the FastAPI application
    import uvicorn
    
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=True,
        use_colors=settings.log_colored
    )


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Failed to start application: {e}")
        sys.exit(1)
