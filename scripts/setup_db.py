#!/usr/bin/env python3
"""
Database setup script.
"""
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_database():
    """Setup database with migrations."""
    print("Setting up database...")
    
    try:
        # Import after adding to path
        import subprocess
        import os
        
        # Change to project directory
        os.chdir(project_root)
        
        # Run Alembic migrations
        print("Running database migrations...")
        result = subprocess.run(
            ["poetry", "run", "alembic", "upgrade", "head"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✓ Database migrations completed successfully")
            print(result.stdout)
        else:
            print("✗ Database migration failed:")
            print(result.stderr)
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Database setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_initial_migration():
    """Create initial migration if none exists."""
    print("Creating initial migration...")
    
    try:
        import subprocess
        import os
        
        # Change to project directory
        os.chdir(project_root)
        
        # Create initial migration
        result = subprocess.run(
            ["poetry", "run", "alembic", "revision", "--autogenerate", "-m", "Initial migration"],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✓ Initial migration created")
            print(result.stdout)
        else:
            print("Migration creation output:")
            print(result.stdout)
            print(result.stderr)
            
        return True
        
    except Exception as e:
        print(f"✗ Migration creation failed: {e}")
        return False

if __name__ == "__main__":
    print("PXBLangs Database Setup")
    print("=" * 30)
    
    # First try to run existing migrations
    if not setup_database():
        print("\nTrying to create initial migration...")
        if create_initial_migration():
            print("\nRunning migrations again...")
            setup_database()
    
    print("\nDatabase setup completed!")
