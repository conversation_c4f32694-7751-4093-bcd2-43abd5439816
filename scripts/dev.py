#!/usr/bin/env python3
"""
Development environment startup script.
"""
import asyncio
import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.config import settings


def create_env_file():
    """Create .env file from example if it doesn't exist."""
    env_file = project_root / ".env"
    env_example = project_root / ".env.example"
    
    if not env_file.exists() and env_example.exists():
        print("Creating .env file from .env.example...")
        env_file.write_text(env_example.read_text())
        print("✓ .env file created. Please review and update the settings.")
        return True
    elif not env_file.exists():
        print("Creating basic .env file...")
        env_content = """# Application settings
APP_NAME=PXBLangs
APP_VERSION=0.1.0
DEBUG=true
ENVIRONMENT=development

# Database settings
DATABASE_URL=mysql+pymysql://root:password@localhost:3306/pxblangs
DATABASE_HOST=localhost
DATABASE_PORT=3306
DATABASE_NAME=pxblangs
DATABASE_USER=root
DATABASE_PASSWORD=password

# Redis settings
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT settings
SECRET_KEY=your-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Logging settings
LOG_LEVEL=DEBUG
LOG_DIR=logs
LOG_JSON=false
LOG_COLORED=true

# Celery settings
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2

# CORS settings
CORS_ORIGINS=["http://localhost:3000","http://localhost:8080","http://127.0.0.1:3000"]
"""
        env_file.write_text(env_content)
        print("✓ Basic .env file created. Please review and update the settings.")
        return True
    
    return False


def check_services():
    """Check if required services are running."""
    print("Checking required services...")
    
    # Check MySQL
    try:
        import pymysql
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='password'
        )
        connection.close()
        print("✓ MySQL is running")
    except Exception as e:
        print(f"✗ MySQL connection failed: {e}")
        print("Please ensure MySQL is running on localhost:3306")
        return False
    
    # Check Redis
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        print("✓ Redis is running")
    except Exception as e:
        print(f"✗ Redis connection failed: {e}")
        print("Please ensure Redis is running on localhost:6379")
        return False
    
    return True


def setup_database():
    """Setup database for development."""
    print("Setting up database...")
    
    try:
        import pymysql
        
        # Connect to MySQL server
        connection = pymysql.connect(
            host='localhost',
            port=3306,
            user='root',
            password='password'
        )
        
        cursor = connection.cursor()
        
        # Create database if it doesn't exist
        cursor.execute("CREATE DATABASE IF NOT EXISTS pxblangs CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✓ Database 'pxblangs' created/verified")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"✗ Failed to setup database: {e}")
        return False


def install_dependencies():
    """Install Python dependencies."""
    print("Installing dependencies...")
    
    try:
        import subprocess
        
        # Install dependencies using poetry
        result = subprocess.run(
            ["poetry", "install"],
            cwd=project_root,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✓ Dependencies installed")
            return True
        else:
            print(f"✗ Failed to install dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Failed to install dependencies: {e}")
        print("Please ensure Poetry is installed: pip install poetry")
        return False


def run_migrations():
    """Run database migrations."""
    print("Running database migrations...")
    
    try:
        import subprocess
        
        # Initialize Alembic if needed
        alembic_dir = project_root / "alembic"
        if not alembic_dir.exists():
            print("Initializing Alembic...")
            subprocess.run(
                ["alembic", "init", "alembic"],
                cwd=project_root,
                check=True
            )
        
        # Run migrations
        result = subprocess.run(
            ["alembic", "upgrade", "head"],
            cwd=project_root,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✓ Database migrations completed")
            return True
        else:
            print(f"✗ Migration failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"✗ Failed to run migrations: {e}")
        return False


async def main():
    """Main development setup function."""
    print("Setting up development environment...")
    print("=" * 50)
    
    # Create .env file if needed
    create_env_file()
    
    # Install dependencies
    if not install_dependencies():
        print("Failed to install dependencies. Please install manually.")
        sys.exit(1)
    
    # Check services
    if not check_services():
        print("\nPlease start the required services:")
        print("- MySQL: brew services start mysql (macOS) or systemctl start mysql (Linux)")
        print("- Redis: brew services start redis (macOS) or systemctl start redis (Linux)")
        sys.exit(1)
    
    # Setup database
    if not setup_database():
        print("Failed to setup database. Please check MySQL configuration.")
        sys.exit(1)
    
    # Run migrations
    if not run_migrations():
        print("Failed to run migrations. Please check database configuration.")
        sys.exit(1)
    
    print("=" * 50)
    print("✓ Development environment setup completed!")
    print("\nTo start the application:")
    print("  python scripts/start.py")
    print("\nOr for development with auto-reload:")
    print("  uvicorn app.main:app --reload --host 0.0.0.0 --port 8000")
    print("\nAPI Documentation will be available at:")
    print("  http://localhost:8000/docs")
    print("\nDefault admin credentials:")
    print("  Username: admin")
    print("  Password: admin123")
    print("  (Please change these in production!)")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nSetup interrupted by user")
    except Exception as e:
        print(f"Setup failed: {e}")
        sys.exit(1)
